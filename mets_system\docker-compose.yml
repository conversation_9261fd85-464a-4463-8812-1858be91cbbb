version: '3.8'

services:
  # MySQL Database
  db:
    image: mysql:8.0
    container_name: mets_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: mets_root_password
      MYSQL_DATABASE: mets_system
      MYSQL_USER: mets_user
      MYSQL_PASSWORD: mets_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./database/sample_data.sql:/docker-entrypoint-initdb.d/02-sample_data.sql
    networks:
      - mets_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis (Optional - for caching and sessions)
  redis:
    image: redis:7-alpine
    container_name: mets_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - mets_network
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # METS Application
  web:
    build: .
    container_name: mets_app
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      FLASK_ENV: production
      SECRET_KEY: your-production-secret-key-here
      MYSQL_HOST: db
      MYSQL_PORT: 3306
      MYSQL_USER: mets_user
      MYSQL_PASSWORD: mets_password
      MYSQL_DATABASE: mets_system
      MAIL_SERVER: smtp.gmail.com
      MAIL_PORT: 587
      MAIL_USE_TLS: true
      MAIL_USERNAME: <EMAIL>
      MAIL_PASSWORD: your-app-password
      REDIS_URL: redis://redis:6379/0
    volumes:
      - uploads_data:/app/app/static/uploads
      - ./logs:/app/logs
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - mets_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: mets_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - uploads_data:/var/www/uploads:ro
    depends_on:
      - web
    networks:
      - mets_network

volumes:
  mysql_data:
    driver: local
  uploads_data:
    driver: local

networks:
  mets_network:
    driver: bridge
