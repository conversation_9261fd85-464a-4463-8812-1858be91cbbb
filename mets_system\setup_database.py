#!/usr/bin/env python3
"""
Database setup script for METS system
Creates database and loads schema
"""

import pymysql
import sys
import os

def create_database():
    """Create the METS database"""
    print("🗄️  Creating METS database...")
    
    try:
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # Create database
        cursor.execute("CREATE DATABASE IF NOT EXISTS mets_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("✅ Database 'mets_system' created successfully")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Database creation failed: {e}")
        return False

def load_schema():
    """Load the database schema"""
    print("\n📋 Loading database schema...")
    
    try:
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            database='mets_system',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # Read schema file
        schema_file = os.path.join('database', 'schema.sql')
        if not os.path.exists(schema_file):
            print(f"❌ Schema file not found: {schema_file}")
            return False
        
        with open(schema_file, 'r', encoding='utf-8') as f:
            schema_content = f.read()
        
        # Split into individual statements
        statements = [stmt.strip() for stmt in schema_content.split(';') if stmt.strip()]
        
        executed = 0
        for statement in statements:
            if statement.upper().startswith(('CREATE', 'INSERT', 'ALTER', 'DROP')):
                try:
                    cursor.execute(statement)
                    executed += 1
                except Exception as e:
                    # Skip errors for statements that might already exist
                    if 'already exists' not in str(e).lower():
                        print(f"⚠️  Warning: {e}")
        
        connection.commit()
        print(f"✅ Schema loaded successfully ({executed} statements executed)")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Schema loading failed: {e}")
        return False

def load_sample_data():
    """Load sample data"""
    print("\n📊 Loading sample data...")
    
    try:
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            database='mets_system',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # Read sample data file
        sample_file = os.path.join('database', 'sample_data.sql')
        if not os.path.exists(sample_file):
            print(f"⚠️  Sample data file not found: {sample_file}")
            return True  # Not critical
        
        with open(sample_file, 'r', encoding='utf-8') as f:
            sample_content = f.read()
        
        # Split into individual statements
        statements = [stmt.strip() for stmt in sample_content.split(';') if stmt.strip()]
        
        executed = 0
        for statement in statements:
            if statement.upper().startswith(('INSERT', 'UPDATE')):
                try:
                    cursor.execute(statement)
                    executed += 1
                except Exception as e:
                    # Skip duplicate key errors
                    if 'duplicate' not in str(e).lower():
                        print(f"⚠️  Warning: {e}")
        
        connection.commit()
        print(f"✅ Sample data loaded successfully ({executed} statements executed)")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Sample data loading failed: {e}")
        return False

def verify_setup():
    """Verify the database setup"""
    print("\n🔍 Verifying database setup...")
    
    try:
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            database='mets_system',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # Check tables
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        table_count = len(tables)
        
        print(f"✅ Found {table_count} tables in database")
        
        # Check if admin user exists
        cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
        admin_count = cursor.fetchone()[0]
        
        if admin_count > 0:
            print("✅ Admin user exists")
        else:
            print("⚠️  Admin user not found")
        
        # Check sample data
        cursor.execute("SELECT COUNT(*) FROM material_requests")
        mr_count = cursor.fetchone()[0]
        
        print(f"✅ Found {mr_count} material requests in database")
        
        cursor.close()
        connection.close()
        
        return table_count > 0
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main setup function"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    METS Database Setup                       ║
    ║              Setting up MySQL Database                      ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    steps_completed = 0
    total_steps = 4
    
    # Step 1: Create database
    if create_database():
        steps_completed += 1
    else:
        print("❌ Cannot proceed without database")
        return False
    
    # Step 2: Load schema
    if load_schema():
        steps_completed += 1
    else:
        print("❌ Cannot proceed without schema")
        return False
    
    # Step 3: Load sample data (optional)
    if load_sample_data():
        steps_completed += 1
    
    # Step 4: Verify setup
    if verify_setup():
        steps_completed += 1
    
    print(f"\n📊 Setup Results: {steps_completed}/{total_steps} steps completed")
    
    if steps_completed >= 3:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🎉 DATABASE READY! 🎉                    ║
    ║                                                              ║
    ║  The METS database has been set up successfully.            ║
    ║  You can now run the application with: python app.py        ║
    ║                                                              ║
    ║  Default login credentials:                                  ║
    ║  Username: admin                                             ║
    ║  Password: admin123                                          ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return True
    else:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    ⚠️  SETUP INCOMPLETE ⚠️                  ║
    ║                                                              ║
    ║  Database setup encountered issues.                         ║
    ║  Check the error messages above.                             ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
