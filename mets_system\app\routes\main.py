from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
from sqlalchemy import func, and_, or_
from datetime import datetime, timedelta
from app import db
from app.models.user import User
from app.models.material_request import MaterialRequest
from app.models.workflow import WorkflowAction, Offer, Evaluation

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """Home page - redirect to dashboard if logged in, otherwise show login"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    return redirect(url_for('auth.login'))

@main_bp.route('/dashboard')
@login_required
def dashboard():
    """Main dashboard page"""
    # Get dashboard statistics
    stats = get_dashboard_stats()
    
    # Get recent MRs for the user
    recent_mrs = get_recent_mrs()
    
    # Get pending actions
    pending_actions = get_pending_actions()
    
    # Get workflow statistics
    workflow_stats = get_workflow_stats()
    
    return render_template('main/dashboard.html',
                         stats=stats,
                         recent_mrs=recent_mrs,
                         pending_actions=pending_actions,
                         workflow_stats=workflow_stats)

@main_bp.route('/inbox')
@login_required
def inbox():
    """Material User Inbox - List of MRs by status"""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', 'all')
    priority_filter = request.args.get('priority', 'all')
    search_query = request.args.get('search', '').strip()
    
    # Build query
    query = MaterialRequest.query
    
    # Filter by user's involvement (created by or assigned to)
    if not current_user.is_admin:
        query = query.filter(
            or_(
                MaterialRequest.created_by_id == current_user.id,
                MaterialRequest.assigned_to_id == current_user.id
            )
        )
    
    # Apply filters
    if status_filter != 'all':
        query = query.filter(MaterialRequest.status == status_filter)
    
    if priority_filter != 'all':
        query = query.filter(MaterialRequest.priority == priority_filter)
    
    if search_query:
        query = query.filter(
            or_(
                MaterialRequest.mr_number.contains(search_query),
                MaterialRequest.title.contains(search_query),
                MaterialRequest.description.contains(search_query)
            )
        )
    
    # Order by most recent first
    query = query.order_by(MaterialRequest.updated_at.desc())
    
    # Paginate results
    per_page = 20
    mrs = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Get filter options
    status_options = get_status_options()
    priority_options = ['LOW', 'NORMAL', 'HIGH', 'URGENT']
    
    return render_template('main/inbox.html',
                         mrs=mrs,
                         status_filter=status_filter,
                         priority_filter=priority_filter,
                         search_query=search_query,
                         status_options=status_options,
                         priority_options=priority_options)

@main_bp.route('/material-register')
@login_required
def material_register():
    """Material Register - Search and export material requests"""
    page = request.args.get('page', 1, type=int)
    
    # Date filters
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    
    # Other filters
    status_filter = request.args.get('status', 'all')
    stage_filter = request.args.get('stage', 'all')
    creator_filter = request.args.get('creator', 'all')
    search_query = request.args.get('search', '').strip()
    
    # Build query
    query = MaterialRequest.query
    
    # Apply date filters
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(MaterialRequest.created_at >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            # Add one day to include the entire day
            date_to_obj = date_to_obj + timedelta(days=1)
            query = query.filter(MaterialRequest.created_at < date_to_obj)
        except ValueError:
            pass
    
    # Apply other filters
    if status_filter != 'all':
        query = query.filter(MaterialRequest.status == status_filter)
    
    if stage_filter != 'all':
        query = query.filter(MaterialRequest.current_stage == stage_filter)
    
    if creator_filter != 'all':
        query = query.filter(MaterialRequest.created_by_id == creator_filter)
    
    if search_query:
        query = query.filter(
            or_(
                MaterialRequest.mr_number.contains(search_query),
                MaterialRequest.title.contains(search_query),
                MaterialRequest.description.contains(search_query)
            )
        )
    
    # Order by most recent first
    query = query.order_by(MaterialRequest.created_at.desc())
    
    # Paginate results
    per_page = 50
    mrs = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Get filter options
    status_options = get_status_options()
    stage_options = get_stage_options()
    creator_options = get_creator_options()
    
    return render_template('main/material_register.html',
                         mrs=mrs,
                         date_from=date_from,
                         date_to=date_to,
                         status_filter=status_filter,
                         stage_filter=stage_filter,
                         creator_filter=creator_filter,
                         search_query=search_query,
                         status_options=status_options,
                         stage_options=stage_options,
                         creator_options=creator_options)

def get_dashboard_stats():
    """Get dashboard statistics"""
    stats = {}
    
    # Total MRs
    stats['total_mrs'] = MaterialRequest.query.count()
    
    # MRs by status
    stats['pending_mrs'] = MaterialRequest.query.filter_by(status='PENDING').count()
    stats['approved_mrs'] = MaterialRequest.query.filter_by(status='APPROVED').count()
    stats['completed_mrs'] = MaterialRequest.query.filter_by(status='COMPLETED').count()
    stats['rejected_mrs'] = MaterialRequest.query.filter_by(status='REJECTED').count()
    
    # User-specific stats
    if not current_user.is_admin:
        stats['my_mrs'] = MaterialRequest.query.filter_by(created_by_id=current_user.id).count()
        stats['assigned_to_me'] = MaterialRequest.query.filter_by(assigned_to_id=current_user.id).count()
        stats['pending_my_action'] = MaterialRequest.query.filter(
            and_(
                MaterialRequest.assigned_to_id == current_user.id,
                MaterialRequest.status == 'PENDING'
            )
        ).count()
    else:
        stats['my_mrs'] = stats['total_mrs']
        stats['assigned_to_me'] = stats['pending_mrs']
        stats['pending_my_action'] = stats['pending_mrs']
    
    # This month's statistics
    current_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    stats['this_month_mrs'] = MaterialRequest.query.filter(
        MaterialRequest.created_at >= current_month
    ).count()
    
    return stats

def get_recent_mrs():
    """Get recent MRs for the user"""
    query = MaterialRequest.query
    
    if not current_user.is_admin:
        query = query.filter(
            or_(
                MaterialRequest.created_by_id == current_user.id,
                MaterialRequest.assigned_to_id == current_user.id
            )
        )
    
    return query.order_by(MaterialRequest.updated_at.desc()).limit(10).all()

def get_pending_actions():
    """Get MRs pending user action"""
    if current_user.is_admin:
        return MaterialRequest.query.filter_by(status='PENDING').limit(10).all()
    else:
        return MaterialRequest.query.filter(
            and_(
                MaterialRequest.assigned_to_id == current_user.id,
                MaterialRequest.status == 'PENDING'
            )
        ).limit(10).all()

def get_workflow_stats():
    """Get workflow stage statistics"""
    from config import Config
    
    stats = {}
    for stage in Config.WORKFLOW_STAGES:
        stats[stage] = MaterialRequest.query.filter_by(current_stage=stage).count()
    
    return stats

def get_status_options():
    """Get available status options"""
    return ['DRAFT', 'PENDING', 'APPROVED', 'REJECTED', 'RETURNED', 'COMPLETED', 'CANCELLED']

def get_stage_options():
    """Get available workflow stage options"""
    from config import Config
    return Config.WORKFLOW_STAGES

def get_creator_options():
    """Get list of users who have created MRs"""
    creators = db.session.query(User).join(MaterialRequest).distinct().all()
    return [(user.id, user.full_name) for user in creators]

# Import redirect here to avoid circular import
from flask import redirect, url_for
