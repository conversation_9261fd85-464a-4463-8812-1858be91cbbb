#!/usr/bin/env python3
"""
Test application for METS system
Minimal version to test core functionality
"""

import os
from flask import Flask, render_template_string, request, redirect, url_for, flash, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON><PERSON><PERSON>anager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash
from datetime import datetime

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-secret-key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:@localhost/mets_system'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Simple User model for testing
class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    role = db.Column(db.String(50), default='MATERIAL_SPECIALIST')
    department = db.Column(db.String(100))
    is_admin = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            if user.is_active:
                login_user(user)
                flash(f'Welcome back, {user.first_name}!', 'success')
                return redirect(url_for('dashboard'))
            else:
                flash('Account is deactivated.', 'error')
        else:
            flash('Invalid username or password.', 'error')
    
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('You have been logged out.', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Get some basic stats
    total_users = User.query.count()
    
    # Try to get MR stats if table exists
    try:
        mr_count = db.session.execute("SELECT COUNT(*) FROM material_requests").scalar()
    except:
        mr_count = 0
    
    stats = {
        'total_users': total_users,
        'total_mrs': mr_count,
        'user_role': current_user.role,
        'user_department': current_user.department
    }
    
    return render_template_string(DASHBOARD_TEMPLATE, user=current_user, stats=stats)

@app.route('/profile')
@login_required
def profile():
    return render_template_string(PROFILE_TEMPLATE, user=current_user)

@app.route('/test-db')
def test_db():
    """Test database connection"""
    try:
        # Test basic query
        user_count = User.query.count()
        
        # Test table existence
        tables = db.session.execute("SHOW TABLES").fetchall()
        table_names = [table[0] for table in tables]
        
        return render_template_string(TEST_DB_TEMPLATE, 
                                    user_count=user_count, 
                                    tables=table_names)
    except Exception as e:
        return f"Database test failed: {str(e)}"

# HTML Templates
LOGIN_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>METS System - Login</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 400px; margin: 50px auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #2c5aa0; margin: 0; }
        .header p { color: #666; margin: 5px 0 0 0; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        .btn { background-color: #2c5aa0; color: white; padding: 12px 20px; border: none; border-radius: 4px; cursor: pointer; width: 100%; font-size: 16px; }
        .btn:hover { background-color: #1e3d6f; }
        .alert { padding: 10px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>METS System</h1>
            <p>Material Management System</p>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" class="btn">Login</button>
        </form>
        
        <div style="margin-top: 20px; text-align: center; color: #666; font-size: 14px;">
            <p>Default credentials:</p>
            <p><strong>Username:</strong> admin<br><strong>Password:</strong> admin123</p>
        </div>
    </div>
</body>
</html>
"""

DASHBOARD_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>METS System - Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
        .header { background-color: #2c5aa0; color: white; padding: 15px 20px; }
        .header h1 { margin: 0; display: inline-block; }
        .user-info { float: right; }
        .nav { background-color: #1e3d6f; padding: 10px 20px; }
        .nav a { color: white; text-decoration: none; margin-right: 20px; }
        .nav a:hover { text-decoration: underline; }
        .container { padding: 20px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #2c5aa0; }
        .stat-label { color: #666; margin-top: 5px; }
        .welcome { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .alert { padding: 10px; margin-bottom: 20px; border-radius: 4px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    </style>
</head>
<body>
    <div class="header">
        <h1>METS System</h1>
        <div class="user-info">
            Welcome, {{ user.full_name }} ({{ user.role }}) | <a href="{{ url_for('logout') }}" style="color: white;">Logout</a>
        </div>
        <div style="clear: both;"></div>
    </div>
    
    <div class="nav">
        <a href="{{ url_for('dashboard') }}">Dashboard</a>
        <a href="{{ url_for('profile') }}">Profile</a>
        <a href="{{ url_for('test_db') }}">Test Database</a>
    </div>
    
    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_users }}</div>
                <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_mrs }}</div>
                <div class="stat-label">Material Requests</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ user.role }}</div>
                <div class="stat-label">Your Role</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ user.department or 'N/A' }}</div>
                <div class="stat-label">Department</div>
            </div>
        </div>
        
        <div class="welcome">
            <h2>🎉 METS System Test - SUCCESS!</h2>
            <p>Congratulations! The METS (Material Management System) is working correctly.</p>
            
            <h3>✅ What's Working:</h3>
            <ul>
                <li><strong>Database Connection:</strong> Successfully connected to MySQL</li>
                <li><strong>User Authentication:</strong> Login/logout functionality working</li>
                <li><strong>User Management:</strong> User data loading from database</li>
                <li><strong>Session Management:</strong> User sessions properly maintained</li>
                <li><strong>Basic UI:</strong> Responsive web interface</li>
            </ul>
            
            <h3>🚀 Next Steps:</h3>
            <ul>
                <li>Install additional dependencies for full functionality</li>
                <li>Add Material Request management features</li>
                <li>Implement workflow management</li>
                <li>Add AI-powered features</li>
                <li>Set up email notifications</li>
            </ul>
            
            <h3>📊 System Information:</h3>
            <ul>
                <li><strong>Logged in as:</strong> {{ user.full_name }} ({{ user.username }})</li>
                <li><strong>Role:</strong> {{ user.role }}</li>
                <li><strong>Department:</strong> {{ user.department or 'Not specified' }}</li>
                <li><strong>Admin Status:</strong> {{ 'Yes' if user.is_admin else 'No' }}</li>
                <li><strong>Login Time:</strong> {{ moment().format('YYYY-MM-DD HH:mm:ss') }}</li>
            </ul>
        </div>
    </div>
</body>
</html>
"""

PROFILE_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>METS System - Profile</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
        .header { background-color: #2c5aa0; color: white; padding: 15px 20px; }
        .header h1 { margin: 0; display: inline-block; }
        .user-info { float: right; }
        .nav { background-color: #1e3d6f; padding: 10px 20px; }
        .nav a { color: white; text-decoration: none; margin-right: 20px; }
        .nav a:hover { text-decoration: underline; }
        .container { padding: 20px; max-width: 600px; margin: 0 auto; }
        .profile-card { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .profile-row { display: flex; margin-bottom: 15px; }
        .profile-label { font-weight: bold; width: 150px; color: #333; }
        .profile-value { color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>METS System</h1>
        <div class="user-info">
            Welcome, {{ user.full_name }} | <a href="{{ url_for('logout') }}" style="color: white;">Logout</a>
        </div>
        <div style="clear: both;"></div>
    </div>
    
    <div class="nav">
        <a href="{{ url_for('dashboard') }}">Dashboard</a>
        <a href="{{ url_for('profile') }}">Profile</a>
        <a href="{{ url_for('test_db') }}">Test Database</a>
    </div>
    
    <div class="container">
        <div class="profile-card">
            <h2>User Profile</h2>
            
            <div class="profile-row">
                <div class="profile-label">Username:</div>
                <div class="profile-value">{{ user.username }}</div>
            </div>
            
            <div class="profile-row">
                <div class="profile-label">Full Name:</div>
                <div class="profile-value">{{ user.full_name }}</div>
            </div>
            
            <div class="profile-row">
                <div class="profile-label">Email:</div>
                <div class="profile-value">{{ user.email }}</div>
            </div>
            
            <div class="profile-row">
                <div class="profile-label">Role:</div>
                <div class="profile-value">{{ user.role }}</div>
            </div>
            
            <div class="profile-row">
                <div class="profile-label">Department:</div>
                <div class="profile-value">{{ user.department or 'Not specified' }}</div>
            </div>
            
            <div class="profile-row">
                <div class="profile-label">Admin Status:</div>
                <div class="profile-value">{{ 'Administrator' if user.is_admin else 'Regular User' }}</div>
            </div>
            
            <div class="profile-row">
                <div class="profile-label">Account Status:</div>
                <div class="profile-value">{{ 'Active' if user.is_active else 'Inactive' }}</div>
            </div>
            
            <div class="profile-row">
                <div class="profile-label">Member Since:</div>
                <div class="profile-value">{{ user.created_at.strftime('%Y-%m-%d') if user.created_at else 'Unknown' }}</div>
            </div>
        </div>
    </div>
</body>
</html>
"""

TEST_DB_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>METS System - Database Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
        .header { background-color: #2c5aa0; color: white; padding: 15px 20px; }
        .header h1 { margin: 0; display: inline-block; }
        .nav { background-color: #1e3d6f; padding: 10px 20px; }
        .nav a { color: white; text-decoration: none; margin-right: 20px; }
        .nav a:hover { text-decoration: underline; }
        .container { padding: 20px; max-width: 800px; margin: 0 auto; }
        .test-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .success { color: #28a745; }
        .table-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
        .table-item { background: #f8f9fa; padding: 10px; border-radius: 4px; text-align: center; }
    </style>
</head>
<body>
    <div class="header">
        <h1>METS System - Database Test</h1>
    </div>
    
    <div class="nav">
        <a href="{{ url_for('dashboard') }}">Dashboard</a>
        <a href="{{ url_for('profile') }}">Profile</a>
        <a href="{{ url_for('test_db') }}">Test Database</a>
    </div>
    
    <div class="container">
        <div class="test-card">
            <h2>✅ Database Connection Test</h2>
            <p class="success">Database connection successful!</p>
            
            <h3>Statistics:</h3>
            <ul>
                <li><strong>Total Users:</strong> {{ user_count }}</li>
                <li><strong>Total Tables:</strong> {{ tables|length }}</li>
            </ul>
            
            <h3>Database Tables:</h3>
            <div class="table-list">
                {% for table in tables %}
                    <div class="table-item">{{ table }}</div>
                {% endfor %}
            </div>
        </div>
    </div>
</body>
</html>
"""

if __name__ == '__main__':
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    METS System - Test App                    ║
    ║              Testing Core Functionality                      ║
    ║                                                              ║
    ║  Starting test server...                                     ║
    ║  Open your browser to: http://localhost:5000                ║
    ║                                                              ║
    ║  Default login credentials:                                  ║
    ║  Username: admin                                             ║
    ║  Password: admin123                                          ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
