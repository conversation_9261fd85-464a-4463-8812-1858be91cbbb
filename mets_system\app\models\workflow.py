from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from app import db

class WorkflowAction(db.Model):
    __tablename__ = 'workflow_actions'

    id = db.Column(db.Integer, primary_key=True)
    material_request_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>('material_requests.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Action details
    stage = db.Column(db.String(50), nullable=False)
    action = db.Column(db.String(20), nullable=False)  # APPROVE, REJECT, RETURN, COMMENT
    comments = db.Column(db.Text, nullable=True)

    # Timing
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def to_dict(self):
        """Convert workflow action to dictionary"""
        return {
            'id': self.id,
            'stage': self.stage,
            'action': self.action,
            'comments': self.comments,
            'user': self.user.full_name if self.user else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<WorkflowAction {self.stage}: {self.action}>'


class WorkflowTemplate(db.Model):
    __tablename__ = 'workflow_templates'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    is_default = db.Column(db.Boolean, default=False, nullable=False)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    steps = db.relationship('WorkflowStep', backref='template', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<WorkflowTemplate {self.name}>'


class WorkflowStep(db.Model):
    __tablename__ = 'workflow_steps'

    id = db.Column(db.Integer, primary_key=True)
    template_id = db.Column(db.Integer, db.ForeignKey('workflow_templates.id'), nullable=False)

    # Step details
    step_number = db.Column(db.Integer, nullable=False)
    stage_name = db.Column(db.String(50), nullable=False)
    display_name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)

    # Approval settings
    required_role = db.Column(db.String(50), nullable=True)
    required_group_id = db.Column(db.Integer, db.ForeignKey('groups.id'), nullable=True)
    is_parallel = db.Column(db.Boolean, default=False, nullable=False)  # Can multiple people approve simultaneously
    is_optional = db.Column(db.Boolean, default=False, nullable=False)

    # Timing
    sla_hours = db.Column(db.Integer, nullable=True)  # Service Level Agreement in hours

    # Conditions
    conditions = db.Column(db.JSON, nullable=True)  # JSON field for complex conditions

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    required_group = db.relationship('Group', backref='workflow_steps')

    def __repr__(self):
        return f'<WorkflowStep {self.step_number}: {self.stage_name}>'


class Offer(db.Model):
    __tablename__ = 'offers'

    id = db.Column(db.Integer, primary_key=True)
    material_request_id = db.Column(db.Integer, db.ForeignKey('material_requests.id'), nullable=False)

    # Supplier information
    supplier_name = db.Column(db.String(200), nullable=False)
    supplier_contact = db.Column(db.String(100), nullable=True)
    supplier_email = db.Column(db.String(120), nullable=True)
    supplier_phone = db.Column(db.String(20), nullable=True)

    # Offer details
    offer_number = db.Column(db.String(50), nullable=True)
    offer_date = db.Column(db.Date, nullable=True)
    validity_date = db.Column(db.Date, nullable=True)

    # Pricing
    total_amount = db.Column(db.Decimal(15, 2), nullable=True)
    currency = db.Column(db.String(3), default='USD', nullable=False)
    payment_terms = db.Column(db.String(200), nullable=True)
    delivery_terms = db.Column(db.String(200), nullable=True)
    delivery_time = db.Column(db.String(100), nullable=True)

    # Status
    status = db.Column(db.String(50), default='RECEIVED', nullable=False)  # RECEIVED, UNDER_EVALUATION, ACCEPTED, REJECTED

    # Evaluation scores
    technical_score = db.Column(db.Decimal(5, 2), nullable=True)  # Out of 100
    commercial_score = db.Column(db.Decimal(5, 2), nullable=True)  # Out of 100
    overall_score = db.Column(db.Decimal(5, 2), nullable=True)  # Out of 100

    # Additional information
    remarks = db.Column(db.Text, nullable=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    offer_items = db.relationship('OfferItem', backref='offer', lazy='dynamic', cascade='all, delete-orphan')

    def to_dict(self):
        """Convert offer to dictionary"""
        return {
            'id': self.id,
            'supplier_name': self.supplier_name,
            'offer_number': self.offer_number,
            'offer_date': self.offer_date.isoformat() if self.offer_date else None,
            'total_amount': float(self.total_amount) if self.total_amount else None,
            'currency': self.currency,
            'status': self.status,
            'technical_score': float(self.technical_score) if self.technical_score else None,
            'commercial_score': float(self.commercial_score) if self.commercial_score else None,
            'overall_score': float(self.overall_score) if self.overall_score else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<Offer {self.supplier_name}: {self.offer_number}>'


class OfferItem(db.Model):
    __tablename__ = 'offer_items'

    id = db.Column(db.Integer, primary_key=True)
    offer_id = db.Column(db.Integer, db.ForeignKey('offers.id'), nullable=False)
    material_request_item_id = db.Column(db.Integer, db.ForeignKey('material_request_items.id'), nullable=True)

    # Item details
    description = db.Column(db.Text, nullable=False)
    specification = db.Column(db.Text, nullable=True)
    quantity = db.Column(db.Decimal(10, 2), nullable=False)
    unit = db.Column(db.String(20), nullable=False)

    # Pricing
    unit_price = db.Column(db.Decimal(15, 2), nullable=False)
    total_price = db.Column(db.Decimal(15, 2), nullable=False)

    # Additional information
    manufacturer = db.Column(db.String(100), nullable=True)
    model_number = db.Column(db.String(100), nullable=True)
    delivery_time = db.Column(db.String(100), nullable=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    mr_item = db.relationship('MaterialRequestItem', backref='offer_items')

    def __repr__(self):
        return f'<OfferItem {self.description[:50]}>'


class Evaluation(db.Model):
    __tablename__ = 'evaluations'

    id = db.Column(db.Integer, primary_key=True)
    material_request_id = db.Column(db.Integer, db.ForeignKey('material_requests.id'), nullable=False)
    offer_id = db.Column(db.Integer, db.ForeignKey('offers.id'), nullable=False)
    evaluator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Evaluation type
    evaluation_type = db.Column(db.String(20), nullable=False)  # TECHNICAL, COMMERCIAL

    # Scores and criteria
    criteria_scores = db.Column(db.JSON, nullable=True)  # JSON field for detailed scoring
    total_score = db.Column(db.Decimal(5, 2), nullable=True)

    # Comments and recommendations
    comments = db.Column(db.Text, nullable=True)
    recommendation = db.Column(db.String(20), nullable=True)  # ACCEPT, REJECT, CONDITIONAL

    # Status
    status = db.Column(db.String(20), default='DRAFT', nullable=False)  # DRAFT, SUBMITTED, APPROVED

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    submitted_at = db.Column(db.DateTime, nullable=True)

    # Relationships
    offer = db.relationship('Offer', backref='evaluations')
    evaluator = db.relationship('User', backref='evaluations')

    def to_dict(self):
        """Convert evaluation to dictionary"""
        return {
            'id': self.id,
            'evaluation_type': self.evaluation_type,
            'total_score': float(self.total_score) if self.total_score else None,
            'recommendation': self.recommendation,
            'status': self.status,
            'evaluator': self.evaluator.full_name if self.evaluator else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'submitted_at': self.submitted_at.isoformat() if self.submitted_at else None
        }

    def __repr__(self):
        return f'<Evaluation {self.evaluation_type}: {self.total_score}>'
