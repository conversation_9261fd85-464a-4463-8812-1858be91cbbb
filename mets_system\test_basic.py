#!/usr/bin/env python3
"""
Basic test script for METS system
Tests core functionality without full dependencies
"""

import sys
import os
from datetime import datetime

def test_imports():
    """Test if core imports work"""
    print("🧪 Testing core imports...")
    
    try:
        import flask
        print(f"✅ Flask {flask.__version__} imported successfully")
    except ImportError as e:
        print(f"❌ Flask import failed: {e}")
        return False
    
    try:
        import flask_sqlalchemy
        print(f"✅ Flask-SQLAlchemy imported successfully")
    except ImportError as e:
        print(f"❌ Flask-SQLAlchemy import failed: {e}")
        return False
    
    try:
        import sqlalchemy
        print(f"✅ SQLAlchemy {sqlalchemy.__version__} imported successfully")
    except ImportError as e:
        print(f"❌ SQLAlchemy import failed: {e}")
        return False
    
    try:
        import pymysql
        print(f"✅ PyMySQL {pymysql.__version__} imported successfully")
    except ImportError as e:
        print(f"❌ PyMySQL import failed: {e}")
        return False
    
    try:
        import dotenv
        print(f"✅ python-dotenv imported successfully")
    except ImportError as e:
        print(f"❌ python-dotenv import failed: {e}")
        return False
    
    return True

def test_flask_app():
    """Test basic Flask app creation"""
    print("\n🧪 Testing Flask app creation...")
    
    try:
        from flask import Flask
        app = Flask(__name__)
        
        @app.route('/')
        def hello():
            return "METS System - Basic Test"
        
        print("✅ Flask app created successfully")
        return True, app
    except Exception as e:
        print(f"❌ Flask app creation failed: {e}")
        return False, None

def test_config():
    """Test configuration loading"""
    print("\n🧪 Testing configuration...")
    
    try:
        # Test if config.py can be imported
        sys.path.insert(0, os.path.dirname(__file__))
        from config import Config
        
        print("✅ Configuration class imported successfully")
        print(f"   - Database URI pattern: {Config.SQLALCHEMY_DATABASE_URI[:20]}...")
        print(f"   - Workflow stages: {len(Config.WORKFLOW_STAGES)} stages")
        print(f"   - User roles: {len(Config.USER_ROLES)} roles")
        
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_models():
    """Test if models can be imported"""
    print("\n🧪 Testing model imports...")
    
    try:
        # Add app directory to path
        app_path = os.path.join(os.path.dirname(__file__), 'app')
        sys.path.insert(0, app_path)
        
        # Test basic model structure
        from models.user import User
        print("✅ User model imported successfully")
        
        from models.material_request import MaterialRequest
        print("✅ MaterialRequest model imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ Model import failed: {e}")
        print(f"   Error details: {str(e)}")
        return False

def test_database_connection():
    """Test database connection (if available)"""
    print("\n🧪 Testing database connection...")
    
    try:
        import pymysql
        
        # Try to connect to default XAMPP MySQL
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            charset='utf8mb4'
        )
        
        if connection:
            print("✅ MySQL connection successful (XAMPP default)")
            
            # Check if database exists
            cursor = connection.cursor()
            cursor.execute("SHOW DATABASES LIKE 'mets_system'")
            result = cursor.fetchone()
            
            if result:
                print("✅ METS database exists")
            else:
                print("⚠️  METS database not found (can be created)")
            
            cursor.close()
            connection.close()
            return True
        
    except Exception as e:
        print(f"⚠️  Database connection failed: {e}")
        print("   This is expected if MySQL/XAMPP is not running")
        return False

def test_app_factory():
    """Test the app factory pattern"""
    print("\n🧪 Testing app factory...")
    
    try:
        # Add app directory to path
        app_path = os.path.join(os.path.dirname(__file__), 'app')
        sys.path.insert(0, app_path)
        
        # Mock the database for testing
        os.environ['MYSQL_HOST'] = 'localhost'
        os.environ['MYSQL_USER'] = 'root'
        os.environ['MYSQL_PASSWORD'] = ''
        os.environ['MYSQL_DATABASE'] = 'mets_system'
        
        # Try to create app
        from app import create_app
        app = create_app('testing')
        
        print("✅ App factory works successfully")
        print(f"   - App name: {app.name}")
        print(f"   - Debug mode: {app.debug}")
        
        return True, app
    except Exception as e:
        print(f"❌ App factory test failed: {e}")
        print(f"   Error details: {str(e)}")
        return False, None

def run_basic_server_test(app):
    """Test if the server can start"""
    print("\n🧪 Testing server startup...")
    
    try:
        # Test if we can get the test client
        with app.test_client() as client:
            print("✅ Test client created successfully")
            return True
    except Exception as e:
        print(f"❌ Server test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    METS System - Basic Test                  ║
    ║              Testing Core Functionality                      ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    print(f"Python version: {sys.version}")
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Run tests
    tests_passed = 0
    total_tests = 6
    
    # Test 1: Core imports
    if test_imports():
        tests_passed += 1
    
    # Test 2: Basic Flask app
    flask_success, flask_app = test_flask_app()
    if flask_success:
        tests_passed += 1
    
    # Test 3: Configuration
    if test_config():
        tests_passed += 1
    
    # Test 4: Models
    if test_models():
        tests_passed += 1
    
    # Test 5: Database connection
    if test_database_connection():
        tests_passed += 1
    
    # Test 6: App factory
    app_factory_success, mets_app = test_app_factory()
    if app_factory_success:
        tests_passed += 1
        
        # Bonus test: Server startup
        if run_basic_server_test(mets_app):
            print("✅ Bonus: Server startup test passed")
    
    # Results
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🎉 ALL TESTS PASSED! 🎉                  ║
    ║                                                              ║
    ║  The METS system core functionality is working correctly.   ║
    ║  You can proceed with full installation and testing.        ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return True
    elif tests_passed >= 4:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                  ⚠️  PARTIAL SUCCESS ⚠️                     ║
    ║                                                              ║
    ║  Core functionality works, but some components need setup.  ║
    ║  Check the failed tests above for details.                  ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return True
    else:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    ❌ TESTS FAILED ❌                       ║
    ║                                                              ║
    ║  Multiple core components are not working correctly.        ║
    ║  Please check the error messages above.                     ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
