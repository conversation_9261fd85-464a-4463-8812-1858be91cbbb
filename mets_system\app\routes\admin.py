from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from sqlalchemy import func, and_, or_
from app import db
from app.models.user import User, Group, UserGroup, UserActivity
from app.models.material_request import MaterialRequest
from app.models.workflow import WorkflowTemplate, WorkflowStep
from app.models.attachment import Template

admin_bp = Blueprint('admin', __name__)

@admin_bp.before_request
@login_required
def require_admin():
    """Require admin privileges for all admin routes"""
    if not current_user.is_admin:
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('main.dashboard'))

@admin_bp.route('/dashboard')
def dashboard():
    """Admin dashboard"""
    # Get system statistics
    stats = get_admin_stats()
    
    # Get recent activities
    recent_activities = UserActivity.query.order_by(UserActivity.created_at.desc()).limit(20).all()
    
    # Get system health metrics
    health_metrics = get_system_health()
    
    return render_template('admin/dashboard.html',
                         stats=stats,
                         recent_activities=recent_activities,
                         health_metrics=health_metrics)

@admin_bp.route('/users')
def users():
    """User management page"""
    page = request.args.get('page', 1, type=int)
    search_query = request.args.get('search', '').strip()
    role_filter = request.args.get('role', 'all')
    status_filter = request.args.get('status', 'all')
    
    # Build query
    query = User.query
    
    if search_query:
        query = query.filter(
            or_(
                User.username.contains(search_query),
                User.email.contains(search_query),
                User.first_name.contains(search_query),
                User.last_name.contains(search_query),
                User.employee_id.contains(search_query)
            )
        )
    
    if role_filter != 'all':
        query = query.filter(User.role == role_filter)
    
    if status_filter == 'active':
        query = query.filter(User.is_active == True)
    elif status_filter == 'inactive':
        query = query.filter(User.is_active == False)
    
    # Order by most recent first
    query = query.order_by(User.created_at.desc())
    
    # Paginate results
    per_page = 20
    users = query.paginate(page=page, per_page=per_page, error_out=False)
    
    # Get filter options
    from config import Config
    roles = Config.USER_ROLES
    
    return render_template('admin/users.html',
                         users=users,
                         search_query=search_query,
                         role_filter=role_filter,
                         status_filter=status_filter,
                         roles=roles)

@admin_bp.route('/users/create', methods=['GET', 'POST'])
def create_user():
    """Create new user"""
    if request.method == 'POST':
        try:
            # Check if username or email already exists
            username = request.form.get('username', '').strip()
            email = request.form.get('email', '').strip()
            
            if User.query.filter_by(username=username).first():
                flash('Username already exists.', 'error')
                return render_template('admin/create_user.html')
            
            if User.query.filter_by(email=email).first():
                flash('Email already exists.', 'error')
                return render_template('admin/create_user.html')
            
            # Create user
            user = User(
                username=username,
                email=email,
                first_name=request.form.get('first_name', '').strip(),
                last_name=request.form.get('last_name', '').strip(),
                employee_id=request.form.get('employee_id', '').strip(),
                role=request.form.get('role', 'MATERIAL_SPECIALIST'),
                department=request.form.get('department', '').strip(),
                phone=request.form.get('phone', '').strip(),
                extension=request.form.get('extension', '').strip(),
                is_active=bool(request.form.get('is_active')),
                is_admin=bool(request.form.get('is_admin'))
            )
            
            # Set password
            password = request.form.get('password', '')
            if len(password) < 6:
                flash('Password must be at least 6 characters long.', 'error')
                return render_template('admin/create_user.html')
            
            user.set_password(password)
            
            db.session.add(user)
            db.session.commit()
            
            # Log activity
            activity = UserActivity(
                user_id=current_user.id,
                action='USER_CREATE',
                description=f'Created user: {user.username}',
                entity_type='User',
                entity_id=user.id,
                ip_address=request.remote_addr
            )
            db.session.add(activity)
            db.session.commit()
            
            flash(f'User {user.username} created successfully!', 'success')
            return redirect(url_for('admin.users'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error creating user: {str(e)}', 'error')
    
    # Get roles for the form
    from config import Config
    roles = Config.USER_ROLES
    
    return render_template('admin/create_user.html', roles=roles)

@admin_bp.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
def edit_user(user_id):
    """Edit user"""
    user = User.query.get_or_404(user_id)
    
    if request.method == 'POST':
        try:
            # Update user information
            user.first_name = request.form.get('first_name', '').strip()
            user.last_name = request.form.get('last_name', '').strip()
            user.email = request.form.get('email', '').strip()
            user.employee_id = request.form.get('employee_id', '').strip()
            user.role = request.form.get('role', 'MATERIAL_SPECIALIST')
            user.department = request.form.get('department', '').strip()
            user.phone = request.form.get('phone', '').strip()
            user.extension = request.form.get('extension', '').strip()
            user.is_active = bool(request.form.get('is_active'))
            user.is_admin = bool(request.form.get('is_admin'))
            
            # Check email uniqueness
            existing_user = User.query.filter(
                User.email == user.email,
                User.id != user.id
            ).first()
            
            if existing_user:
                flash('Email address is already in use by another user.', 'error')
                return render_template('admin/edit_user.html', user=user)
            
            # Update password if provided
            new_password = request.form.get('new_password', '').strip()
            if new_password:
                if len(new_password) < 6:
                    flash('Password must be at least 6 characters long.', 'error')
                    return render_template('admin/edit_user.html', user=user)
                user.set_password(new_password)
            
            user.updated_at = datetime.utcnow()
            db.session.commit()
            
            # Log activity
            activity = UserActivity(
                user_id=current_user.id,
                action='USER_UPDATE',
                description=f'Updated user: {user.username}',
                entity_type='User',
                entity_id=user.id,
                ip_address=request.remote_addr
            )
            db.session.add(activity)
            db.session.commit()
            
            flash('User updated successfully!', 'success')
            return redirect(url_for('admin.users'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error updating user: {str(e)}', 'error')
    
    # Get roles for the form
    from config import Config
    roles = Config.USER_ROLES
    
    return render_template('admin/edit_user.html', user=user, roles=roles)

@admin_bp.route('/users/<int:user_id>/delete', methods=['POST'])
def delete_user(user_id):
    """Delete user (deactivate)"""
    user = User.query.get_or_404(user_id)
    
    if user.id == current_user.id:
        flash('You cannot delete your own account.', 'error')
        return redirect(url_for('admin.users'))
    
    try:
        # Deactivate instead of deleting to preserve data integrity
        user.is_active = False
        user.updated_at = datetime.utcnow()
        db.session.commit()
        
        # Log activity
        activity = UserActivity(
            user_id=current_user.id,
            action='USER_DEACTIVATE',
            description=f'Deactivated user: {user.username}',
            entity_type='User',
            entity_id=user.id,
            ip_address=request.remote_addr
        )
        db.session.add(activity)
        db.session.commit()
        
        flash(f'User {user.username} deactivated successfully.', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'Error deactivating user: {str(e)}', 'error')
    
    return redirect(url_for('admin.users'))

@admin_bp.route('/groups')
def groups():
    """Group management page"""
    groups = Group.query.order_by(Group.name).all()
    return render_template('admin/groups.html', groups=groups)

@admin_bp.route('/groups/create', methods=['GET', 'POST'])
def create_group():
    """Create new group"""
    if request.method == 'POST':
        try:
            name = request.form.get('name', '').strip()
            
            if Group.query.filter_by(name=name).first():
                flash('Group name already exists.', 'error')
                return render_template('admin/create_group.html')
            
            group = Group(
                name=name,
                description=request.form.get('description', '').strip(),
                is_active=bool(request.form.get('is_active'))
            )
            
            db.session.add(group)
            db.session.commit()
            
            # Log activity
            activity = UserActivity(
                user_id=current_user.id,
                action='GROUP_CREATE',
                description=f'Created group: {group.name}',
                entity_type='Group',
                entity_id=group.id,
                ip_address=request.remote_addr
            )
            db.session.add(activity)
            db.session.commit()
            
            flash(f'Group {group.name} created successfully!', 'success')
            return redirect(url_for('admin.groups'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error creating group: {str(e)}', 'error')
    
    return render_template('admin/create_group.html')

@admin_bp.route('/reports')
def reports():
    """System reports page"""
    # Get report data
    reports_data = get_reports_data()
    
    return render_template('admin/reports.html', reports_data=reports_data)

@admin_bp.route('/settings')
def settings():
    """System settings page"""
    return render_template('admin/settings.html')

@admin_bp.route('/activities')
def activities():
    """User activities log"""
    page = request.args.get('page', 1, type=int)
    user_filter = request.args.get('user', 'all')
    action_filter = request.args.get('action', 'all')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    
    # Build query
    query = UserActivity.query
    
    if user_filter != 'all':
        query = query.filter(UserActivity.user_id == user_filter)
    
    if action_filter != 'all':
        query = query.filter(UserActivity.action == action_filter)
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(UserActivity.created_at >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
            query = query.filter(UserActivity.created_at < date_to_obj)
        except ValueError:
            pass
    
    # Order by most recent first
    query = query.order_by(UserActivity.created_at.desc())
    
    # Paginate results
    per_page = 50
    activities = query.paginate(page=page, per_page=per_page, error_out=False)
    
    # Get filter options
    users = User.query.filter_by(is_active=True).order_by(User.first_name, User.last_name).all()
    actions = db.session.query(UserActivity.action).distinct().all()
    actions = [action[0] for action in actions]
    
    return render_template('admin/activities.html',
                         activities=activities,
                         users=users,
                         actions=actions,
                         user_filter=user_filter,
                         action_filter=action_filter,
                         date_from=date_from,
                         date_to=date_to)

def get_admin_stats():
    """Get admin dashboard statistics"""
    stats = {}
    
    # User statistics
    stats['total_users'] = User.query.count()
    stats['active_users'] = User.query.filter_by(is_active=True).count()
    stats['admin_users'] = User.query.filter_by(is_admin=True).count()
    
    # MR statistics
    stats['total_mrs'] = MaterialRequest.query.count()
    stats['pending_mrs'] = MaterialRequest.query.filter_by(status='PENDING').count()
    stats['completed_mrs'] = MaterialRequest.query.filter_by(status='COMPLETED').count()
    
    # This month's statistics
    current_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    stats['new_users_this_month'] = User.query.filter(User.created_at >= current_month).count()
    stats['new_mrs_this_month'] = MaterialRequest.query.filter(MaterialRequest.created_at >= current_month).count()
    
    # Activity statistics
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    stats['activities_today'] = UserActivity.query.filter(UserActivity.created_at >= today).count()
    
    return stats

def get_system_health():
    """Get system health metrics"""
    health = {}
    
    # Database health
    try:
        db.session.execute('SELECT 1')
        health['database'] = 'healthy'
    except Exception:
        health['database'] = 'error'
    
    # Workflow health (check for bottlenecks)
    bottlenecks = db.session.query(
        MaterialRequest.current_stage,
        func.count(MaterialRequest.id).label('count')
    ).filter_by(status='PENDING').group_by(MaterialRequest.current_stage).all()
    
    health['workflow_bottlenecks'] = [(stage, count) for stage, count in bottlenecks if count > 10]
    
    # Storage health (simplified)
    health['storage'] = 'healthy'  # Would implement actual storage checks
    
    return health

def get_reports_data():
    """Get data for reports"""
    reports = {}
    
    # MR statistics by month
    monthly_mrs = db.session.query(
        func.date_format(MaterialRequest.created_at, '%Y-%m').label('month'),
        func.count(MaterialRequest.id).label('count')
    ).group_by('month').order_by('month').limit(12).all()
    
    reports['monthly_mrs'] = [{'month': month, 'count': count} for month, count in monthly_mrs]
    
    # MR statistics by status
    status_stats = db.session.query(
        MaterialRequest.status,
        func.count(MaterialRequest.id).label('count')
    ).group_by(MaterialRequest.status).all()
    
    reports['status_stats'] = [{'status': status, 'count': count} for status, count in status_stats]
    
    # User activity statistics
    user_activity = db.session.query(
        User.username,
        func.count(UserActivity.id).label('activity_count')
    ).join(UserActivity).group_by(User.id).order_by('activity_count desc').limit(10).all()
    
    reports['top_active_users'] = [{'username': username, 'count': count} for username, count in user_activity]
    
    return reports
