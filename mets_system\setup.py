#!/usr/bin/env python3
"""
METS System Setup Script
Automated setup for the Material Management System
"""

import os
import sys
import subprocess
import mysql.connector
from mysql.connector import Error
import getpass

def print_banner():
    """Print setup banner"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    METS System Setup                         ║
    ║              Material Management System                      ║
    ║                                                              ║
    ║  This script will help you set up the METS system           ║
    ║  including database creation and initial configuration       ║
    ╚══════════════════════════════════════════════════════════════╝
    """)

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        sys.exit(1)
    print(f"✅ Python version: {sys.version.split()[0]}")

def check_mysql_connection():
    """Check MySQL connection and create database if needed"""
    print("\n📊 Setting up database...")
    
    # Get MySQL credentials
    host = input("MySQL Host (default: localhost): ").strip() or "localhost"
    port = input("MySQL Port (default: 3306): ").strip() or "3306"
    username = input("MySQL Username (default: root): ").strip() or "root"
    password = getpass.getpass("MySQL Password: ")
    
    try:
        # Connect to MySQL
        connection = mysql.connector.connect(
            host=host,
            port=int(port),
            user=username,
            password=password
        )
        
        if connection.is_connected():
            print("✅ MySQL connection successful")
            
            cursor = connection.cursor()
            
            # Create database
            cursor.execute("CREATE DATABASE IF NOT EXISTS mets_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("✅ Database 'mets_system' created/verified")
            
            # Check if we should load schema
            cursor.execute("USE mets_system")
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            if not tables:
                print("📋 Loading database schema...")
                load_schema(host, port, username, password)
                
                # Ask about sample data
                load_sample = input("Load sample data? (y/N): ").strip().lower()
                if load_sample in ['y', 'yes']:
                    load_sample_data(host, port, username, password)
            else:
                print("✅ Database tables already exist")
            
            cursor.close()
            connection.close()
            
            return {
                'host': host,
                'port': port,
                'username': username,
                'password': password
            }
            
    except Error as e:
        print(f"❌ MySQL connection failed: {e}")
        return None

def load_schema(host, port, username, password):
    """Load database schema"""
    try:
        schema_file = os.path.join(os.path.dirname(__file__), 'database', 'schema.sql')
        if os.path.exists(schema_file):
            cmd = f'mysql -h {host} -P {port} -u {username} -p{password} mets_system < "{schema_file}"'
            subprocess.run(cmd, shell=True, check=True)
            print("✅ Database schema loaded successfully")
        else:
            print("⚠️  Schema file not found, skipping...")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to load schema: {e}")

def load_sample_data(host, port, username, password):
    """Load sample data"""
    try:
        sample_file = os.path.join(os.path.dirname(__file__), 'database', 'sample_data.sql')
        if os.path.exists(sample_file):
            cmd = f'mysql -h {host} -P {port} -u {username} -p{password} mets_system < "{sample_file}"'
            subprocess.run(cmd, shell=True, check=True)
            print("✅ Sample data loaded successfully")
        else:
            print("⚠️  Sample data file not found, skipping...")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to load sample data: {e}")

def create_env_file(db_config):
    """Create .env file with configuration"""
    print("\n⚙️  Creating environment configuration...")
    
    env_content = f"""# METS System Configuration
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=mets-secret-key-{os.urandom(16).hex()}

# Database Configuration
MYSQL_HOST={db_config['host']}
MYSQL_PORT={db_config['port']}
MYSQL_USER={db_config['username']}
MYSQL_PASSWORD={db_config['password']}
MYSQL_DATABASE=mets_system

# Email Configuration (Update with your SMTP settings)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>

# AI Configuration
AI_ENABLED=true

# File Upload Configuration
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=app/static/uploads
"""
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print("✅ Environment file (.env) created")

def install_dependencies():
    """Install Python dependencies"""
    print("\n📦 Installing Python dependencies...")
    
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True, capture_output=True, text=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def create_upload_directory():
    """Create upload directory"""
    upload_dir = os.path.join('app', 'static', 'uploads')
    os.makedirs(upload_dir, exist_ok=True)
    print(f"✅ Upload directory created: {upload_dir}")

def run_initial_setup():
    """Run initial application setup"""
    print("\n🚀 Running initial application setup...")
    
    try:
        # Import and run database initialization
        from app import create_app, db
        from app.models.user import User
        
        app = create_app()
        with app.app_context():
            # Create tables if they don't exist
            db.create_all()
            
            # Check if admin user exists
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    first_name='System',
                    last_name='Administrator',
                    role='ADMIN',
                    department='IT',
                    is_admin=True,
                    employee_id='EMP001'
                )
                admin.set_password('admin123')
                db.session.add(admin)
                db.session.commit()
                print("✅ Default admin user created")
            else:
                print("✅ Admin user already exists")
        
        return True
    except Exception as e:
        print(f"❌ Application setup failed: {e}")
        return False

def print_completion_message():
    """Print setup completion message"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    Setup Complete! 🎉                       ║
    ║                                                              ║
    ║  Your METS system is now ready to use.                      ║
    ║                                                              ║
    ║  To start the application:                                   ║
    ║    python app.py                                             ║
    ║                                                              ║
    ║  Then open your browser to:                                  ║
    ║    http://localhost:5000                                     ║
    ║                                                              ║
    ║  Default login credentials:                                  ║
    ║    Username: admin                                           ║
    ║    Password: admin123                                        ║
    ║                                                              ║
    ║  Don't forget to:                                            ║
    ║  1. Update email settings in .env file                      ║
    ║  2. Change default admin password                            ║
    ║  3. Create additional users as needed                        ║
    ╚══════════════════════════════════════════════════════════════╝
    """)

def main():
    """Main setup function"""
    print_banner()
    
    # Check Python version
    check_python_version()
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed. Please install dependencies manually.")
        return
    
    # Setup database
    db_config = check_mysql_connection()
    if not db_config:
        print("\n❌ Setup failed. Please check MySQL configuration.")
        return
    
    # Create environment file
    create_env_file(db_config)
    
    # Create upload directory
    create_upload_directory()
    
    # Run initial application setup
    if not run_initial_setup():
        print("\n❌ Setup failed during application initialization.")
        return
    
    # Print completion message
    print_completion_message()

if __name__ == '__main__':
    main()
