#!/usr/bin/env python3
"""
Test individual model imports to find the Decimal issue
"""

import sys
import os

def test_individual_models():
    """Test each model individually"""
    print("🧪 Testing individual model imports...")
    
    # Set up environment
    os.environ['FLASK_ENV'] = 'testing'
    
    models_to_test = [
        'user',
        'material_request', 
        'workflow',
        'attachment'
    ]
    
    for model_name in models_to_test:
        try:
            print(f"   Testing {model_name}...")
            if model_name == 'user':
                from app.models.user import User
                print(f"   ✅ {model_name} imported successfully")
            elif model_name == 'material_request':
                from app.models.material_request import MaterialRequest
                print(f"   ✅ {model_name} imported successfully")
            elif model_name == 'workflow':
                from app.models.workflow import WorkflowAction
                print(f"   ✅ {model_name} imported successfully")
            elif model_name == 'attachment':
                from app.models.attachment import Attachment
                print(f"   ✅ {model_name} imported successfully")
        except Exception as e:
            print(f"   ❌ {model_name} failed: {e}")
            return False, model_name
    
    return True, None

def test_flask_app_creation():
    """Test Flask app creation without models"""
    print("\n🧪 Testing Flask app creation...")
    
    try:
        from flask import Flask
        from flask_sqlalchemy import SQLAlchemy
        
        app = Flask(__name__)
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        
        db = SQLAlchemy(app)
        
        print("✅ Basic Flask app with SQLAlchemy created")
        return True
        
    except Exception as e:
        print(f"❌ Flask app creation failed: {e}")
        return False

def main():
    """Main test function"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    Model Import Test                         ║
    ║              Finding the Decimal Issue                      ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # Test 1: Basic Flask app
    if not test_flask_app_creation():
        print("❌ Basic Flask setup failed")
        return False
    
    # Test 2: Individual models
    success, failed_model = test_individual_models()
    
    if success:
        print("\n✅ All models imported successfully!")
        print("The Decimal issue might be in the app factory or database connection.")
    else:
        print(f"\n❌ Model import failed at: {failed_model}")
        print("This is where the Decimal issue is located.")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
