from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from datetime import datetime, date
import os
import uuid
from app import db
from app.models.user import User, UserActivity
from app.models.material_request import MaterialRequest, MaterialRequestItem
from app.models.attachment import Attachment, Template
from app.services.email_service import send_notification_email
from app.services.ai_service import suggest_next_approver

mr_bp = Blueprint('mr', __name__)

@mr_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """Create new Material Request"""
    if request.method == 'POST':
        try:
            # Create new MR
            mr = MaterialRequest(
                title=request.form.get('title', '').strip(),
                description=request.form.get('description', '').strip(),
                priority=request.form.get('priority', 'NORMAL'),
                category=request.form.get('category', '').strip(),
                project_code=request.form.get('project_code', '').strip(),
                cost_center=request.form.get('cost_center', '').strip(),
                justification=request.form.get('justification', '').strip(),
                technical_specifications=request.form.get('technical_specifications', '').strip(),
                delivery_location=request.form.get('delivery_location', '').strip(),
                created_by_id=current_user.id,
                status='DRAFT'
            )
            
            # Set dates
            if request.form.get('requested_date'):
                mr.requested_date = datetime.strptime(request.form.get('requested_date'), '%Y-%m-%d').date()
            
            if request.form.get('required_date'):
                mr.required_date = datetime.strptime(request.form.get('required_date'), '%Y-%m-%d').date()
            
            # Set estimated cost
            if request.form.get('estimated_cost'):
                mr.estimated_cost = float(request.form.get('estimated_cost'))
            
            db.session.add(mr)
            db.session.flush()  # Get the ID
            
            # Add items
            items_data = request.form.getlist('items')
            for i, item_json in enumerate(items_data):
                if item_json.strip():
                    import json
                    try:
                        item_data = json.loads(item_json)
                        item = MaterialRequestItem(
                            material_request_id=mr.id,
                            description=item_data.get('description', ''),
                            specification=item_data.get('specification', ''),
                            quantity=float(item_data.get('quantity', 0)),
                            unit=item_data.get('unit', ''),
                            unit_price=float(item_data.get('unit_price', 0)) if item_data.get('unit_price') else None,
                            manufacturer=item_data.get('manufacturer', ''),
                            model_number=item_data.get('model_number', ''),
                            remarks=item_data.get('remarks', '')
                        )
                        
                        # Calculate total price
                        if item.unit_price and item.quantity:
                            item.total_price = item.unit_price * item.quantity
                        
                        db.session.add(item)
                    except (json.JSONDecodeError, ValueError) as e:
                        flash(f'Error processing item {i+1}: {str(e)}', 'error')
                        continue
            
            # Handle file uploads
            uploaded_files = request.files.getlist('attachments')
            for file in uploaded_files:
                if file and file.filename:
                    if allowed_file(file.filename):
                        attachment = save_attachment(file, mr.id, current_user.id)
                        if attachment:
                            db.session.add(attachment)
                    else:
                        flash(f'File type not allowed: {file.filename}', 'warning')
            
            # Submit or save as draft
            action = request.form.get('action', 'save_draft')
            if action == 'submit':
                mr.status = 'PENDING'
                mr.current_stage = 'MR_APPROVAL'
                
                # Assign to next approver
                next_approver = mr.get_current_approver()
                if next_approver:
                    mr.assigned_to_id = next_approver.id
                    
                    # Send notification email
                    send_notification_email(
                        to_user=next_approver,
                        subject=f'New Material Request for Approval: {mr.mr_number}',
                        template='mr_approval_request',
                        mr=mr
                    )
            
            db.session.commit()
            
            # Log activity
            activity = UserActivity(
                user_id=current_user.id,
                action='MR_CREATE',
                description=f'Created Material Request {mr.mr_number}',
                entity_type='MaterialRequest',
                entity_id=mr.id,
                ip_address=request.remote_addr
            )
            db.session.add(activity)
            db.session.commit()
            
            if action == 'submit':
                flash(f'Material Request {mr.mr_number} created and submitted for approval!', 'success')
            else:
                flash(f'Material Request {mr.mr_number} saved as draft.', 'info')
            
            return redirect(url_for('mr.view', id=mr.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error creating Material Request: {str(e)}', 'error')
    
    # Get templates for the form
    templates = Template.query.filter(
        (Template.is_public == True) | (Template.created_by_id == current_user.id)
    ).filter_by(is_active=True).all()
    
    return render_template('mr/create.html', templates=templates)

@mr_bp.route('/<int:id>')
@login_required
def view(id):
    """View Material Request details"""
    mr = MaterialRequest.query.get_or_404(id)
    
    # Check permissions
    if not can_view_mr(mr, current_user):
        flash('You do not have permission to view this Material Request.', 'error')
        return redirect(url_for('main.inbox'))
    
    # Get workflow history
    workflow_history = mr.workflow_actions.order_by('created_at').all()
    
    # Get offers if in offer collection stage
    offers = []
    if mr.current_stage in ['RECEIVING_OFFERS', 'TECHNICAL_EVAL', 'COMMERCIAL_EVAL']:
        offers = mr.offers.all()
    
    # Get evaluations
    evaluations = mr.evaluations.all()
    
    return render_template('mr/view.html',
                         mr=mr,
                         workflow_history=workflow_history,
                         offers=offers,
                         evaluations=evaluations)

@mr_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """Edit Material Request"""
    mr = MaterialRequest.query.get_or_404(id)
    
    # Check permissions
    if not mr.can_edit(current_user):
        flash('You do not have permission to edit this Material Request.', 'error')
        return redirect(url_for('mr.view', id=id))
    
    if request.method == 'POST':
        try:
            # Update MR fields
            mr.title = request.form.get('title', '').strip()
            mr.description = request.form.get('description', '').strip()
            mr.priority = request.form.get('priority', 'NORMAL')
            mr.category = request.form.get('category', '').strip()
            mr.project_code = request.form.get('project_code', '').strip()
            mr.cost_center = request.form.get('cost_center', '').strip()
            mr.justification = request.form.get('justification', '').strip()
            mr.technical_specifications = request.form.get('technical_specifications', '').strip()
            mr.delivery_location = request.form.get('delivery_location', '').strip()
            
            # Update dates
            if request.form.get('requested_date'):
                mr.requested_date = datetime.strptime(request.form.get('requested_date'), '%Y-%m-%d').date()
            
            if request.form.get('required_date'):
                mr.required_date = datetime.strptime(request.form.get('required_date'), '%Y-%m-%d').date()
            
            # Update estimated cost
            if request.form.get('estimated_cost'):
                mr.estimated_cost = float(request.form.get('estimated_cost'))
            
            mr.updated_at = datetime.utcnow()
            
            # Handle new file uploads
            uploaded_files = request.files.getlist('attachments')
            for file in uploaded_files:
                if file and file.filename:
                    if allowed_file(file.filename):
                        attachment = save_attachment(file, mr.id, current_user.id)
                        if attachment:
                            db.session.add(attachment)
                    else:
                        flash(f'File type not allowed: {file.filename}', 'warning')
            
            db.session.commit()
            
            # Log activity
            activity = UserActivity(
                user_id=current_user.id,
                action='MR_UPDATE',
                description=f'Updated Material Request {mr.mr_number}',
                entity_type='MaterialRequest',
                entity_id=mr.id,
                ip_address=request.remote_addr
            )
            db.session.add(activity)
            db.session.commit()
            
            flash('Material Request updated successfully!', 'success')
            return redirect(url_for('mr.view', id=id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error updating Material Request: {str(e)}', 'error')
    
    return render_template('mr/edit.html', mr=mr)

@mr_bp.route('/<int:id>/approve', methods=['POST'])
@login_required
def approve(id):
    """Approve Material Request"""
    mr = MaterialRequest.query.get_or_404(id)
    
    # Check permissions
    if not mr.can_approve(current_user):
        flash('You do not have permission to approve this Material Request.', 'error')
        return redirect(url_for('mr.view', id=id))
    
    action = request.form.get('action', 'approve')
    comments = request.form.get('comments', '').strip()
    
    try:
        # Advance workflow
        mr.advance_workflow(current_user, action.upper(), comments)
        
        # Log activity
        activity = UserActivity(
            user_id=current_user.id,
            action=f'MR_{action.upper()}',
            description=f'{action.title()} Material Request {mr.mr_number}',
            entity_type='MaterialRequest',
            entity_id=mr.id,
            ip_address=request.remote_addr
        )
        db.session.add(activity)
        db.session.commit()
        
        # Send notification to next approver or creator
        if action == 'approve' and mr.assigned_to_id:
            next_approver = User.query.get(mr.assigned_to_id)
            if next_approver:
                send_notification_email(
                    to_user=next_approver,
                    subject=f'Material Request for Approval: {mr.mr_number}',
                    template='mr_approval_request',
                    mr=mr
                )
        elif action in ['reject', 'return']:
            creator = User.query.get(mr.created_by_id)
            if creator:
                send_notification_email(
                    to_user=creator,
                    subject=f'Material Request {action.title()}: {mr.mr_number}',
                    template=f'mr_{action}',
                    mr=mr,
                    comments=comments
                )
        
        flash(f'Material Request {action}d successfully!', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'Error processing approval: {str(e)}', 'error')
    
    return redirect(url_for('mr.view', id=id))

@mr_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """Delete Material Request (only drafts)"""
    mr = MaterialRequest.query.get_or_404(id)
    
    # Check permissions - only creator can delete drafts
    if mr.created_by_id != current_user.id or mr.status != 'DRAFT':
        flash('You can only delete your own draft Material Requests.', 'error')
        return redirect(url_for('mr.view', id=id))
    
    try:
        # Delete associated files
        for attachment in mr.attachments:
            attachment.delete_file()
        
        # Delete the MR (cascade will handle related records)
        db.session.delete(mr)
        
        # Log activity
        activity = UserActivity(
            user_id=current_user.id,
            action='MR_DELETE',
            description=f'Deleted Material Request {mr.mr_number}',
            entity_type='MaterialRequest',
            entity_id=mr.id,
            ip_address=request.remote_addr
        )
        db.session.add(activity)
        db.session.commit()
        
        flash('Material Request deleted successfully.', 'success')
        return redirect(url_for('main.inbox'))
        
    except Exception as e:
        db.session.rollback()
        flash(f'Error deleting Material Request: {str(e)}', 'error')
        return redirect(url_for('mr.view', id=id))

@mr_bp.route('/template/<int:template_id>')
@login_required
def load_template(template_id):
    """Load template data for creating MR"""
    template = Template.query.get_or_404(template_id)
    
    # Check permissions
    if not template.is_public and template.created_by_id != current_user.id:
        return jsonify({'error': 'Access denied'}), 403
    
    # Increment usage count
    template.increment_usage()
    
    return jsonify(template.template_data)

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def save_attachment(file, mr_id, user_id):
    """Save uploaded file as attachment"""
    try:
        # Generate unique filename
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4().hex}_{filename}"
        
        # Create upload directory if it doesn't exist
        upload_dir = current_app.config['UPLOAD_FOLDER']
        if not os.path.exists(upload_dir):
            os.makedirs(upload_dir)
        
        # Save file
        file_path = os.path.join(upload_dir, unique_filename)
        file.save(file_path)
        
        # Get file size
        file_size = os.path.getsize(file_path)
        
        # Create attachment record
        attachment = Attachment(
            material_request_id=mr_id,
            uploaded_by_id=user_id,
            filename=unique_filename,
            original_filename=filename,
            file_path=file_path,
            file_size=file_size,
            mime_type=file.mimetype
        )
        
        return attachment
        
    except Exception as e:
        print(f"Error saving attachment: {str(e)}")
        return None

def can_view_mr(mr, user):
    """Check if user can view the MR"""
    if user.is_admin:
        return True
    
    # Creator can always view
    if mr.created_by_id == user.id:
        return True
    
    # Assigned user can view
    if mr.assigned_to_id == user.id:
        return True
    
    # Users who have acted on the MR can view
    if mr.workflow_actions.filter_by(user_id=user.id).first():
        return True
    
    return False
