#!/usr/bin/env python3
"""
Simple table creation script that bypasses problematic imports
"""

import os
import sys
from datetime import datetime

def create_tables_direct():
    """Create tables directly without full app factory"""
    print("🗄️  Creating database tables directly...")
    
    try:
        # Set environment variables
        os.environ['FLASK_ENV'] = 'development'
        os.environ['MYSQL_HOST'] = 'localhost'
        os.environ['MYSQL_USER'] = 'root'
        os.environ['MYSQL_PASSWORD'] = ''
        os.environ['MYSQL_DATABASE'] = 'mets_system'
        
        # Create minimal Flask app
        from flask import Flask
        from flask_sqlalchemy import SQLAlchemy
        
        app = Flask(__name__)
        app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:@localhost/mets_system'
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        app.config['SECRET_KEY'] = 'test-key'
        
        db = SQLAlchemy(app)
        
        # Import models directly
        from app.models.user import User
        from app.models.material_request import MaterialRequest, MaterialRequestItem
        from app.models.workflow import WorkflowAction, WorkflowTemplate, WorkflowStep, Offer, OfferItem, Evaluation
        from app.models.attachment import Attachment, Template
        
        with app.app_context():
            # Create all tables
            db.create_all()
            print("✅ Database tables created successfully")
            
            # Check what tables were created
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            print(f"✅ Created {len(tables)} tables: {', '.join(tables)}")
            
            return True, app, db
            
    except Exception as e:
        print(f"❌ Table creation failed: {e}")
        return False, None, None

def create_admin_user_direct(app, db):
    """Create admin user directly"""
    print("\n👤 Creating admin user...")
    
    try:
        from app.models.user import User
        
        with app.app_context():
            # Check if admin exists
            admin = User.query.filter_by(username='admin').first()
            
            if admin:
                print("✅ Admin user already exists")
                return True
            
            # Create admin user
            admin = User(
                username='admin',
                email='<EMAIL>',
                first_name='System',
                last_name='Administrator',
                role='ADMIN',
                department='IT',
                is_admin=True,
                employee_id='EMP001'
            )
            admin.set_password('admin123')
            
            db.session.add(admin)
            db.session.commit()
            
            print("✅ Admin user created successfully")
            print("   Username: admin")
            print("   Password: admin123")
            
            return True
            
    except Exception as e:
        print(f"❌ Admin user creation failed: {e}")
        return False

def verify_setup_direct(app, db):
    """Verify the database setup"""
    print("\n🔍 Verifying database setup...")
    
    try:
        from app.models.user import User
        
        with app.app_context():
            # Check tables
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            print(f"✅ Found {len(tables)} tables")
            
            # Check users
            user_count = User.query.count()
            print(f"✅ Found {user_count} users")
            
            # Check admin
            admin = User.query.filter_by(username='admin').first()
            if admin:
                print("✅ Admin user verified")
            else:
                print("⚠️  Admin user not found")
            
            return len(tables) > 0 and user_count > 0
            
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main setup function"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    METS Database Setup                       ║
    ║              Simple Table Creation                           ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    steps_completed = 0
    total_steps = 3
    
    # Step 1: Create tables
    success, app, db = create_tables_direct()
    if success:
        steps_completed += 1
    else:
        print("❌ Cannot proceed without tables")
        return False
    
    # Step 2: Create admin user
    if create_admin_user_direct(app, db):
        steps_completed += 1
    
    # Step 3: Verify setup
    if verify_setup_direct(app, db):
        steps_completed += 1
    
    print(f"\n📊 Setup Results: {steps_completed}/{total_steps} steps completed")
    
    if steps_completed >= 2:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🎉 DATABASE READY! 🎉                    ║
    ║                                                              ║
    ║  The METS database has been set up successfully.            ║
    ║  You can now test the application.                          ║
    ║                                                              ║
    ║  Default login credentials:                                  ║
    ║  Username: admin                                             ║
    ║  Password: admin123                                          ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return True
    else:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    ⚠️  SETUP INCOMPLETE ⚠️                  ║
    ║                                                              ║
    ║  Database setup encountered issues.                         ║
    ║  Check the error messages above.                             ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
