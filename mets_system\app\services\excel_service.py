"""
Excel Generation Service for METS System
Generates Excel reports and exports
"""

import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
import pandas as pd
from datetime import datetime
import tempfile
from app.models.material_request import MaterialRequest
from app.models.user import User

def generate_register_excel(filters=None):
    """Generate Excel export for Material Register"""
    try:
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        temp_file.close()
        
        # Build query based on filters
        query = MaterialRequest.query
        
        if filters:
            if filters.get('status') and filters['status'] != 'all':
                query = query.filter(MaterialRequest.status == filters['status'])
            
            if filters.get('stage') and filters['stage'] != 'all':
                query = query.filter(MaterialRequest.current_stage == filters['stage'])
            
            if filters.get('creator') and filters['creator'] != 'all':
                query = query.filter(MaterialRequest.created_by_id == filters['creator'])
            
            if filters.get('date_from'):
                try:
                    date_from = datetime.strptime(filters['date_from'], '%Y-%m-%d')
                    query = query.filter(MaterialRequest.created_at >= date_from)
                except ValueError:
                    pass
            
            if filters.get('date_to'):
                try:
                    date_to = datetime.strptime(filters['date_to'], '%Y-%m-%d')
                    query = query.filter(MaterialRequest.created_at <= date_to)
                except ValueError:
                    pass
        
        # Get data
        mrs = query.order_by(MaterialRequest.created_at.desc()).all()
        
        # Create workbook
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Material Register"
        
        # Define styles
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Headers
        headers = [
            'MR Number', 'Title', 'Description', 'Priority', 'Status', 'Current Stage',
            'Created By', 'Assigned To', 'Created Date', 'Updated Date', 'Required Date',
            'Estimated Cost', 'Currency', 'Category', 'Project Code', 'Cost Center'
        ]
        
        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center')
        
        # Write data
        for row, mr in enumerate(mrs, 2):
            data = [
                mr.mr_number,
                mr.title,
                mr.description,
                mr.priority,
                mr.status,
                mr.current_stage.replace('_', ' ').title(),
                mr.creator.full_name if mr.creator else '',
                mr.assignee.full_name if mr.assignee else '',
                mr.created_at.strftime('%Y-%m-%d %H:%M') if mr.created_at else '',
                mr.updated_at.strftime('%Y-%m-%d %H:%M') if mr.updated_at else '',
                mr.required_date.strftime('%Y-%m-%d') if mr.required_date else '',
                float(mr.estimated_cost) if mr.estimated_cost else '',
                mr.currency,
                mr.category,
                mr.project_code,
                mr.cost_center
            ]
            
            for col, value in enumerate(data, 1):
                cell = ws.cell(row=row, column=col, value=value)
                cell.border = border
                
                # Format dates and numbers
                if col in [9, 10, 11]:  # Date columns
                    cell.alignment = Alignment(horizontal='center')
                elif col == 12:  # Cost column
                    if value:
                        cell.number_format = '#,##0.00'
                        cell.alignment = Alignment(horizontal='right')
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # Add summary sheet
        ws_summary = wb.create_sheet("Summary")
        
        # Summary data
        total_mrs = len(mrs)
        status_counts = {}
        stage_counts = {}
        priority_counts = {}
        
        for mr in mrs:
            # Count by status
            status_counts[mr.status] = status_counts.get(mr.status, 0) + 1
            
            # Count by stage
            stage_counts[mr.current_stage] = stage_counts.get(mr.current_stage, 0) + 1
            
            # Count by priority
            priority_counts[mr.priority] = priority_counts.get(mr.priority, 0) + 1
        
        # Write summary
        summary_row = 1
        
        # Total
        ws_summary.cell(row=summary_row, column=1, value="Total Material Requests").font = Font(bold=True)
        ws_summary.cell(row=summary_row, column=2, value=total_mrs)
        summary_row += 2
        
        # By Status
        ws_summary.cell(row=summary_row, column=1, value="By Status").font = Font(bold=True)
        summary_row += 1
        for status, count in status_counts.items():
            ws_summary.cell(row=summary_row, column=1, value=status)
            ws_summary.cell(row=summary_row, column=2, value=count)
            summary_row += 1
        summary_row += 1
        
        # By Stage
        ws_summary.cell(row=summary_row, column=1, value="By Workflow Stage").font = Font(bold=True)
        summary_row += 1
        for stage, count in stage_counts.items():
            ws_summary.cell(row=summary_row, column=1, value=stage.replace('_', ' ').title())
            ws_summary.cell(row=summary_row, column=2, value=count)
            summary_row += 1
        summary_row += 1
        
        # By Priority
        ws_summary.cell(row=summary_row, column=1, value="By Priority").font = Font(bold=True)
        summary_row += 1
        for priority, count in priority_counts.items():
            ws_summary.cell(row=summary_row, column=1, value=priority)
            ws_summary.cell(row=summary_row, column=2, value=count)
            summary_row += 1
        
        # Auto-adjust summary columns
        ws_summary.column_dimensions['A'].width = 25
        ws_summary.column_dimensions['B'].width = 15
        
        # Save workbook
        wb.save(temp_file.name)
        
        return temp_file.name
        
    except Exception as e:
        print(f"Error generating Excel register: {str(e)}")
        raise e

def generate_mr_items_excel(mr):
    """Generate Excel export for Material Request items"""
    try:
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        temp_file.close()
        
        # Create workbook
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = f"MR {mr.mr_number} Items"
        
        # Define styles
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # MR Information
        ws.cell(row=1, column=1, value="Material Request Information").font = Font(bold=True, size=14)
        ws.merge_cells('A1:H1')
        
        mr_info = [
            ['MR Number:', mr.mr_number],
            ['Title:', mr.title],
            ['Status:', mr.status],
            ['Created By:', mr.creator.full_name if mr.creator else ''],
            ['Created Date:', mr.created_at.strftime('%Y-%m-%d') if mr.created_at else '']
        ]
        
        for row, (label, value) in enumerate(mr_info, 3):
            ws.cell(row=row, column=1, value=label).font = Font(bold=True)
            ws.cell(row=row, column=2, value=value)
        
        # Items table starts at row 10
        start_row = 10
        
        # Headers
        headers = [
            'Item #', 'Description', 'Specification', 'Quantity', 'Unit',
            'Unit Price', 'Total Price', 'Manufacturer', 'Model Number', 'Remarks'
        ]
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=start_row, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center')
        
        # Items data
        total_amount = 0
        for row, item in enumerate(mr.items, start_row + 1):
            data = [
                row - start_row,
                item.description,
                item.specification,
                float(item.quantity),
                item.unit,
                float(item.unit_price) if item.unit_price else '',
                float(item.total_price) if item.total_price else '',
                item.manufacturer,
                item.model_number,
                item.remarks
            ]
            
            if item.total_price:
                total_amount += float(item.total_price)
            
            for col, value in enumerate(data, 1):
                cell = ws.cell(row=row, column=col, value=value)
                cell.border = border
                
                # Format numbers
                if col in [4, 6, 7]:  # Quantity, Unit Price, Total Price
                    if value:
                        if col == 4:  # Quantity
                            cell.number_format = '#,##0.00'
                        else:  # Prices
                            cell.number_format = '#,##0.00'
                        cell.alignment = Alignment(horizontal='right')
        
        # Total row
        if total_amount > 0:
            total_row = start_row + len(list(mr.items)) + 1
            ws.cell(row=total_row, column=6, value="TOTAL:").font = Font(bold=True)
            ws.cell(row=total_row, column=6).alignment = Alignment(horizontal='right')
            
            total_cell = ws.cell(row=total_row, column=7, value=total_amount)
            total_cell.font = Font(bold=True)
            total_cell.number_format = '#,##0.00'
            total_cell.alignment = Alignment(horizontal='right')
            total_cell.border = border
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # Save workbook
        wb.save(temp_file.name)
        
        return temp_file.name
        
    except Exception as e:
        print(f"Error generating MR items Excel: {str(e)}")
        raise e

def generate_offers_comparison_excel(mr, offers):
    """Generate Excel comparison of offers"""
    try:
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        temp_file.close()
        
        # Create workbook
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Offers Comparison"
        
        # Define styles
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # MR Information
        ws.cell(row=1, column=1, value=f"Offers Comparison - MR {mr.mr_number}").font = Font(bold=True, size=14)
        ws.merge_cells('A1:J1')
        
        ws.cell(row=2, column=1, value=f"Title: {mr.title}")
        ws.cell(row=3, column=1, value=f"Comparison Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        
        # Offers comparison table starts at row 6
        start_row = 6
        
        # Headers
        headers = [
            'Supplier', 'Offer Number', 'Offer Date', 'Total Amount', 'Currency',
            'Payment Terms', 'Delivery Terms', 'Technical Score', 'Commercial Score',
            'Overall Score', 'Status'
        ]
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=start_row, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center')
        
        # Offers data
        for row, offer in enumerate(offers, start_row + 1):
            data = [
                offer.supplier_name,
                offer.offer_number,
                offer.offer_date.strftime('%Y-%m-%d') if offer.offer_date else '',
                float(offer.total_amount) if offer.total_amount else '',
                offer.currency,
                offer.payment_terms,
                offer.delivery_terms,
                float(offer.technical_score) if offer.technical_score else '',
                float(offer.commercial_score) if offer.commercial_score else '',
                float(offer.overall_score) if offer.overall_score else '',
                offer.status
            ]
            
            for col, value in enumerate(data, 1):
                cell = ws.cell(row=row, column=col, value=value)
                cell.border = border
                
                # Format numbers
                if col in [4, 8, 9, 10]:  # Amount and scores
                    if value:
                        if col == 4:  # Amount
                            cell.number_format = '#,##0.00'
                        else:  # Scores
                            cell.number_format = '0.0'
                        cell.alignment = Alignment(horizontal='right')
                elif col == 3:  # Date
                    cell.alignment = Alignment(horizontal='center')
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 30)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # Add detailed items comparison if offers have items
        if any(offer.offer_items.count() > 0 for offer in offers):
            ws_items = wb.create_sheet("Items Comparison")
            
            # Items comparison headers
            item_headers = ['Item Description', 'Specification', 'Quantity', 'Unit']
            
            # Add supplier columns
            for offer in offers:
                item_headers.extend([
                    f"{offer.supplier_name} - Unit Price",
                    f"{offer.supplier_name} - Total Price"
                ])
            
            # Write headers
            for col, header in enumerate(item_headers, 1):
                cell = ws_items.cell(row=1, column=col, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.border = border
                cell.alignment = Alignment(horizontal='center')
            
            # Get all unique items from MR
            mr_items = list(mr.items)
            
            for row, mr_item in enumerate(mr_items, 2):
                # Basic item info
                ws_items.cell(row=row, column=1, value=mr_item.description).border = border
                ws_items.cell(row=row, column=2, value=mr_item.specification).border = border
                ws_items.cell(row=row, column=3, value=float(mr_item.quantity)).border = border
                ws_items.cell(row=row, column=4, value=mr_item.unit).border = border
                
                # Offer prices
                col_offset = 5
                for offer in offers:
                    # Find matching offer item
                    offer_item = offer.offer_items.filter_by(material_request_item_id=mr_item.id).first()
                    
                    if offer_item:
                        unit_price_cell = ws_items.cell(row=row, column=col_offset, value=float(offer_item.unit_price))
                        unit_price_cell.number_format = '#,##0.00'
                        unit_price_cell.alignment = Alignment(horizontal='right')
                        unit_price_cell.border = border
                        
                        total_price_cell = ws_items.cell(row=row, column=col_offset + 1, value=float(offer_item.total_price))
                        total_price_cell.number_format = '#,##0.00'
                        total_price_cell.alignment = Alignment(horizontal='right')
                        total_price_cell.border = border
                    else:
                        ws_items.cell(row=row, column=col_offset, value="N/A").border = border
                        ws_items.cell(row=row, column=col_offset + 1, value="N/A").border = border
                    
                    col_offset += 2
            
            # Auto-adjust items sheet column widths
            for column in ws_items.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 25)
                ws_items.column_dimensions[column_letter].width = adjusted_width
        
        # Save workbook
        wb.save(temp_file.name)
        
        return temp_file.name
        
    except Exception as e:
        print(f"Error generating offers comparison Excel: {str(e)}")
        raise e

def generate_user_activity_excel(activities):
    """Generate Excel export for user activities"""
    try:
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        temp_file.close()
        
        # Create workbook
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "User Activities"
        
        # Define styles
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Headers
        headers = [
            'Date/Time', 'User', 'Action', 'Description', 'Entity Type', 'Entity ID', 'IP Address'
        ]
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center')
        
        # Activities data
        for row, activity in enumerate(activities, 2):
            data = [
                activity.created_at.strftime('%Y-%m-%d %H:%M:%S') if activity.created_at else '',
                activity.user.full_name if activity.user else '',
                activity.action,
                activity.description,
                activity.entity_type,
                activity.entity_id,
                activity.ip_address
            ]
            
            for col, value in enumerate(data, 1):
                cell = ws.cell(row=row, column=col, value=value)
                cell.border = border
                
                if col == 1:  # Date column
                    cell.alignment = Alignment(horizontal='center')
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 40)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # Save workbook
        wb.save(temp_file.name)
        
        return temp_file.name
        
    except Exception as e:
        print(f"Error generating user activity Excel: {str(e)}")
        raise e
