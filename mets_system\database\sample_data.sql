-- METS System Sample Data
-- Sample data for testing and demonstration

USE mets_system;

-- Sample Users
INSERT INTO users (username, email, password_hash, first_name, last_name, role, department, employee_id, phone, is_active) VALUES
('john.smith', '<EMAIL>', 'scrypt:32768:8:1$salt$hash', '<PERSON>', '<PERSON>', 'MATERIAL_SPECIALIST', 'Maintenance', 'EMP002', '******-0101', TRUE),
('sarah.jones', '<EMAIL>', 'scrypt:32768:8:1$salt$hash', '<PERSON>', '<PERSON>', 'TECHNICAL_BUYER', 'Procurement', 'EMP003', '******-0102', TRUE),
('mike.wilson', '<EMAIL>', 'scrypt:32768:8:1$salt$hash', '<PERSON>', '<PERSON>', 'SECTION_HEAD_EXPAT', 'Maintenance', 'EMP004', '******-0103', TRUE),
('lisa.brown', '<EMAIL>', 'scrypt:32768:8:1$salt$hash', '<PERSON>', 'Brown', 'MATERIAL_COORDINATOR', 'Procurement', 'EMP005', '******-0104', TRUE),
('david.garcia', '<EMAIL>', 'scrypt:32768:8:1$salt$hash', 'David', 'Garcia', 'MAINTENANCE_MANAGER', 'Maintenance', 'EMP006', '******-0105', TRUE),
('anna.lee', '<EMAIL>', 'scrypt:32768:8:1$salt$hash', 'Anna', 'Lee', 'COMMERCIAL_COMMITTEE', 'Finance', 'EMP007', '******-0106', TRUE),
('robert.taylor', '<EMAIL>', 'scrypt:32768:8:1$salt$hash', 'Robert', 'Taylor', 'WAREHOUSE_KEEPER', 'Warehouse', 'EMP008', '******-0107', TRUE),
('jennifer.white', '<EMAIL>', 'scrypt:32768:8:1$salt$hash', 'Jennifer', 'White', 'SUPERVISOR', 'Operations', 'EMP009', '******-0108', TRUE),
('carlos.martinez', '<EMAIL>', 'scrypt:32768:8:1$salt$hash', 'Carlos', 'Martinez', 'SECTION_HEAD_LOCAL', 'Maintenance', 'EMP010', '******-0109', TRUE),
('emily.davis', '<EMAIL>', 'scrypt:32768:8:1$salt$hash', 'Emily', 'Davis', 'TECHNICAL_BUYER', 'Procurement', 'EMP011', '******-0110', TRUE);

-- Assign users to groups
INSERT INTO user_groups (user_id, group_id) VALUES
(2, 1), -- John Smith -> Material Specialists
(3, 2), -- Sarah Jones -> Technical Buyers
(4, 3), -- Mike Wilson -> Section Heads
(5, 1), -- Lisa Brown -> Material Specialists (also coordinator)
(6, 4), -- David Garcia -> Maintenance Managers
(7, 5), -- Anna Lee -> Commercial Committee
(8, 6), -- Robert Taylor -> Warehouse Team
(9, 7), -- Jennifer White -> Supervisors
(10, 3), -- Carlos Martinez -> Section Heads
(11, 2); -- Emily Davis -> Technical Buyers

-- Sample Material Requests
INSERT INTO material_requests (mr_number, title, description, priority, category, project_code, cost_center, status, current_stage, created_by_id, assigned_to_id, requested_date, required_date, estimated_cost, currency, justification, technical_specifications, delivery_location) VALUES
('MR-2024-0001', 'Pump Replacement for Unit A', 'Centrifugal pump replacement for cooling water system in Unit A', 'HIGH', 'MECHANICAL', 'PROJ-001', 'CC-MAINT-001', 'PENDING', 'MR_APPROVAL', 2, 4, '2024-01-15', '2024-02-15', 15000.00, 'USD', 'Current pump is showing signs of wear and efficiency loss', 'Centrifugal pump, 150 GPM, 100 ft head, stainless steel construction', 'Unit A Pump House'),

('MR-2024-0002', 'Electrical Components for Control Panel', 'Various electrical components needed for control panel upgrade', 'NORMAL', 'ELECTRICAL', 'PROJ-002', 'CC-ELEC-001', 'PENDING', 'RECEIVING_OFFERS', 3, 5, '2024-01-20', '2024-03-01', 8500.00, 'USD', 'Control panel upgrade to improve automation and safety', 'PLCs, contactors, relays, and wiring as per specification sheet', 'Main Control Room'),

('MR-2024-0003', 'Safety Equipment and PPE', 'Personal protective equipment and safety gear for maintenance team', 'URGENT', 'SAFETY', 'PROJ-003', 'CC-SAFE-001', 'PENDING', 'TECHNICAL_EVAL', 2, 3, '2024-01-25', '2024-02-10', 3200.00, 'USD', 'Current PPE inventory is running low and some items are expired', 'Hard hats, safety glasses, gloves, harnesses, and gas detectors', 'Safety Equipment Storage'),

('MR-2024-0004', 'Instrumentation Calibration Tools', 'Precision instruments for calibration and maintenance', 'NORMAL', 'INSTRUMENTATION', 'PROJ-004', 'CC-INST-001', 'COMPLETED', 'CLOSING_OUT', 11, 8, '2024-01-10', '2024-02-28', 12000.00, 'USD', 'Required for annual calibration program', 'Digital multimeters, pressure calibrators, temperature calibrators', 'Instrument Shop'),

('MR-2024-0005', 'Spare Parts for Compressor', 'Critical spare parts for main air compressor', 'HIGH', 'MECHANICAL', 'PROJ-005', 'CC-MAINT-002', 'DRAFT', 'MR_CREATION', 2, NULL, '2024-02-01', '2024-03-15', 22000.00, 'USD', 'Preventive maintenance and emergency spare parts inventory', 'Compressor valves, gaskets, filters, and oil seals', 'Compressor Room');

-- Sample Material Request Items
INSERT INTO material_request_items (material_request_id, description, specification, quantity, unit, unit_price, total_price, manufacturer, model_number) VALUES
-- Items for MR-2024-0001 (Pump Replacement)
(1, 'Centrifugal Pump', 'Horizontal centrifugal pump, 150 GPM, 100 ft head, 316 SS', 1.00, 'EA', 12000.00, 12000.00, 'Grundfos', 'CR64-2-1'),
(1, 'Pump Coupling', 'Flexible coupling for pump motor connection', 1.00, 'EA', 450.00, 450.00, 'Lovejoy', 'L-150'),
(1, 'Mechanical Seal', 'Cartridge mechanical seal for pump', 2.00, 'EA', 275.00, 550.00, 'John Crane', 'Type 1'),
(1, 'Pump Base', 'Steel base plate with vibration dampeners', 1.00, 'EA', 800.00, 800.00, 'Custom Fab', 'PB-150'),

-- Items for MR-2024-0002 (Electrical Components)
(2, 'PLC Module', 'Programmable Logic Controller main unit', 1.00, 'EA', 2500.00, 2500.00, 'Allen Bradley', 'CompactLogix 5370'),
(2, 'I/O Modules', 'Digital input/output modules', 4.00, 'EA', 350.00, 1400.00, 'Allen Bradley', '1769-IQ16'),
(2, 'Contactors', 'Motor control contactors 30A', 6.00, 'EA', 125.00, 750.00, 'Schneider Electric', 'LC1D32'),
(2, 'Control Relays', 'Auxiliary relays 24VDC', 10.00, 'EA', 45.00, 450.00, 'Omron', 'MY2N-24VDC'),

-- Items for MR-2024-0003 (Safety Equipment)
(3, 'Hard Hats', 'Class E hard hats with chin strap', 20.00, 'EA', 25.00, 500.00, '3M', 'H-700'),
(3, 'Safety Glasses', 'Anti-fog safety glasses with side shields', 30.00, 'EA', 15.00, 450.00, 'Uvex', 'S3200'),
(3, 'Work Gloves', 'Cut-resistant work gloves size L', 50.00, 'PR', 12.00, 600.00, 'Ansell', 'HyFlex 11-541'),
(3, 'Gas Detector', 'Portable 4-gas detector', 5.00, 'EA', 450.00, 2250.00, 'Honeywell', 'BW Ultra'),

-- Items for MR-2024-0004 (Instrumentation)
(4, 'Digital Multimeter', 'True RMS digital multimeter', 3.00, 'EA', 350.00, 1050.00, 'Fluke', '87V'),
(4, 'Pressure Calibrator', 'Pneumatic pressure calibrator 0-300 PSI', 1.00, 'EA', 2800.00, 2800.00, 'Fluke', '719Pro'),
(4, 'Temperature Calibrator', 'Thermocouple/RTD calibrator', 1.00, 'EA', 1900.00, 1900.00, 'Fluke', '754'),
(4, 'Calibration Kit', 'Electrical calibration accessories kit', 1.00, 'SET', 650.00, 650.00, 'Fluke', 'TLK-225');

-- Sample Workflow Actions
INSERT INTO workflow_actions (material_request_id, user_id, stage, action, comments, created_at) VALUES
(1, 2, 'MR_CREATION', 'SUBMIT', 'Initial submission for approval', '2024-01-15 10:30:00'),
(2, 3, 'MR_CREATION', 'SUBMIT', 'Submitted for procurement review', '2024-01-20 14:15:00'),
(2, 4, 'MR_APPROVAL', 'APPROVE', 'Approved for offer collection', '2024-01-22 09:45:00'),
(3, 2, 'MR_CREATION', 'SUBMIT', 'Urgent safety equipment request', '2024-01-25 11:20:00'),
(3, 4, 'MR_APPROVAL', 'APPROVE', 'Approved due to urgency', '2024-01-25 15:30:00'),
(3, 5, 'RECEIVING_OFFERS', 'APPROVE', 'Offers collected, moving to evaluation', '2024-01-28 10:00:00'),
(4, 11, 'MR_CREATION', 'SUBMIT', 'Calibration tools for annual program', '2024-01-10 08:30:00'),
(4, 4, 'MR_APPROVAL', 'APPROVE', 'Approved for procurement', '2024-01-12 13:15:00'),
(4, 5, 'RECEIVING_OFFERS', 'APPROVE', 'Best offers selected', '2024-01-18 16:20:00'),
(4, 3, 'TECHNICAL_EVAL', 'APPROVE', 'Technical specifications verified', '2024-01-22 11:45:00'),
(4, 7, 'COMMERCIAL_EVAL', 'APPROVE', 'Commercial terms acceptable', '2024-01-25 14:30:00'),
(4, 6, 'MANAGEMENT_APPR', 'APPROVE', 'Final approval granted', '2024-01-28 09:15:00'),
(4, 5, 'PURCHASING_PROCESS', 'APPROVE', 'Purchase order issued', '2024-01-30 10:45:00');

-- Sample Offers
INSERT INTO offers (material_request_id, supplier_name, supplier_contact, supplier_email, supplier_phone, offer_number, offer_date, validity_date, total_amount, currency, payment_terms, delivery_terms, delivery_time, status, technical_score, commercial_score, overall_score) VALUES
(2, 'Industrial Electric Supply Co.', 'Tom Johnson', '<EMAIL>', '******-2001', 'IES-2024-0156', '2024-01-23', '2024-02-23', 8200.00, 'USD', 'Net 30 days', 'FOB Destination', '2-3 weeks', 'UNDER_EVALUATION', 85.5, 78.2, 81.85),
(2, 'Automation Components Inc.', 'Maria Rodriguez', '<EMAIL>', '******-2002', 'ACI-Q-789', '2024-01-24', '2024-02-24', 8750.00, 'USD', 'Net 45 days', 'FOB Origin', '3-4 weeks', 'UNDER_EVALUATION', 92.0, 72.5, 82.25),
(2, 'ElectroTech Solutions', 'James Park', '<EMAIL>', '******-2003', 'ETS-2024-Q012', '2024-01-25', '2024-03-01', 7950.00, 'USD', 'Net 30 days', 'FOB Destination', '1-2 weeks', 'UNDER_EVALUATION', 88.5, 85.0, 86.75),

(3, 'Safety First Equipment', 'Linda Chen', '<EMAIL>', '******-3001', 'SFE-Q-2024-045', '2024-01-26', '2024-02-26', 3150.00, 'USD', 'Net 30 days', 'FOB Destination', '1 week', 'ACCEPTED', 90.0, 88.5, 89.25),
(3, 'ProSafe Industrial', 'Robert Kim', '<EMAIL>', '******-3002', 'PSI-2024-0089', '2024-01-27', '2024-02-27', 3350.00, 'USD', 'Net 45 days', 'FOB Origin', '2 weeks', 'REJECTED', 85.0, 75.0, 80.0),

(4, 'Precision Instruments Ltd.', 'Susan Wright', '<EMAIL>', '******-4001', 'PIL-2024-Q156', '2024-01-15', '2024-02-15', 11800.00, 'USD', 'Net 30 days', 'FOB Destination', '2-3 weeks', 'ACCEPTED', 95.0, 92.0, 93.5),
(4, 'Calibration Solutions Inc.', 'Mark Thompson', '<EMAIL>', '******-4002', 'CSI-Q-789012', '2024-01-16', '2024-02-16', 12200.00, 'USD', 'Net 45 days', 'FOB Origin', '3-4 weeks', 'REJECTED', 88.0, 85.0, 86.5);

-- Sample Offer Items
INSERT INTO offer_items (offer_id, material_request_item_id, description, specification, quantity, unit, unit_price, total_price, manufacturer, model_number, delivery_time) VALUES
-- Offer items for Industrial Electric Supply Co. (Offer ID 1)
(1, 5, 'PLC Module', 'Programmable Logic Controller main unit', 1.00, 'EA', 2400.00, 2400.00, 'Allen Bradley', 'CompactLogix 5370', '2 weeks'),
(1, 6, 'I/O Modules', 'Digital input/output modules', 4.00, 'EA', 340.00, 1360.00, 'Allen Bradley', '1769-IQ16', '2 weeks'),
(1, 7, 'Contactors', 'Motor control contactors 30A', 6.00, 'EA', 120.00, 720.00, 'Schneider Electric', 'LC1D32', '1 week'),
(1, 8, 'Control Relays', 'Auxiliary relays 24VDC', 10.00, 'EA', 42.00, 420.00, 'Omron', 'MY2N-24VDC', '1 week'),

-- Offer items for Safety First Equipment (Offer ID 4)
(4, 9, 'Hard Hats', 'Class E hard hats with chin strap', 20.00, 'EA', 23.00, 460.00, '3M', 'H-700', '1 week'),
(4, 10, 'Safety Glasses', 'Anti-fog safety glasses with side shields', 30.00, 'EA', 14.00, 420.00, 'Uvex', 'S3200', '1 week'),
(4, 11, 'Work Gloves', 'Cut-resistant work gloves size L', 50.00, 'PR', 11.50, 575.00, 'Ansell', 'HyFlex 11-541', '1 week'),
(4, 12, 'Gas Detector', 'Portable 4-gas detector', 5.00, 'EA', 439.00, 2195.00, 'Honeywell', 'BW Ultra', '1 week');

-- Sample Evaluations
INSERT INTO evaluations (material_request_id, offer_id, evaluator_id, evaluation_type, total_score, comments, recommendation, status, submitted_at, criteria_scores) VALUES
(2, 1, 3, 'TECHNICAL', 85.5, 'Good technical compliance, all specifications met. Minor concerns about delivery time.', 'ACCEPT', 'SUBMITTED', '2024-01-26 14:30:00', '{"compliance": 90, "quality": 85, "delivery": 80, "support": 87}'),
(2, 1, 7, 'COMMERCIAL', 78.2, 'Competitive pricing but payment terms could be better. Overall acceptable.', 'ACCEPT', 'SUBMITTED', '2024-01-27 10:15:00', '{"price": 75, "terms": 70, "warranty": 85, "reputation": 83}'),

(2, 2, 3, 'TECHNICAL', 92.0, 'Excellent technical specifications and quality. Preferred supplier for automation.', 'ACCEPT', 'SUBMITTED', '2024-01-26 15:45:00', '{"compliance": 95, "quality": 92, "delivery": 88, "support": 93}'),
(2, 2, 7, 'COMMERCIAL', 72.5, 'Higher price point but good value. Extended payment terms are favorable.', 'CONDITIONAL', 'SUBMITTED', '2024-01-27 11:30:00', '{"price": 68, "terms": 80, "warranty": 75, "reputation": 87}'),

(3, 4, 3, 'TECHNICAL', 90.0, 'All safety equipment meets or exceeds specifications. Fast delivery is crucial.', 'ACCEPT', 'SUBMITTED', '2024-01-28 09:00:00', '{"compliance": 95, "quality": 88, "delivery": 92, "certification": 85}'),
(3, 4, 7, 'COMMERCIAL', 88.5, 'Competitive pricing with good terms. Recommended for award.', 'ACCEPT', 'SUBMITTED', '2024-01-28 13:20:00', '{"price": 85, "terms": 90, "warranty": 88, "reputation": 91}');

-- Sample Templates
INSERT INTO templates (name, description, category, template_data, created_by_id, is_public, usage_count) VALUES
('Standard Pump Replacement', 'Template for centrifugal pump replacement requests', 'MECHANICAL', 
'{"title": "Pump Replacement for [UNIT]", "category": "MECHANICAL", "priority": "HIGH", "items": [{"description": "Centrifugal Pump", "specification": "Horizontal centrifugal pump, [GPM] GPM, [HEAD] ft head, 316 SS", "quantity": 1, "unit": "EA"}, {"description": "Pump Coupling", "specification": "Flexible coupling for pump motor connection", "quantity": 1, "unit": "EA"}, {"description": "Mechanical Seal", "specification": "Cartridge mechanical seal for pump", "quantity": 2, "unit": "EA"}]}', 
2, TRUE, 5),

('Electrical Control Panel', 'Template for electrical control panel components', 'ELECTRICAL',
'{"title": "Electrical Components for [PANEL_NAME]", "category": "ELECTRICAL", "priority": "NORMAL", "items": [{"description": "PLC Module", "specification": "Programmable Logic Controller main unit", "quantity": 1, "unit": "EA"}, {"description": "I/O Modules", "specification": "Digital input/output modules", "quantity": 4, "unit": "EA"}, {"description": "Contactors", "specification": "Motor control contactors [RATING]A", "quantity": 6, "unit": "EA"}]}',
3, TRUE, 3),

('Safety Equipment Standard', 'Standard PPE and safety equipment request', 'SAFETY',
'{"title": "Safety Equipment and PPE", "category": "SAFETY", "priority": "NORMAL", "items": [{"description": "Hard Hats", "specification": "Class E hard hats with chin strap", "quantity": 20, "unit": "EA"}, {"description": "Safety Glasses", "specification": "Anti-fog safety glasses with side shields", "quantity": 30, "unit": "EA"}, {"description": "Work Gloves", "specification": "Cut-resistant work gloves", "quantity": 50, "unit": "PR"}]}',
2, TRUE, 8);

-- Sample User Activities (recent activities)
INSERT INTO user_activities (user_id, action, description, entity_type, entity_id, ip_address, created_at) VALUES
(2, 'LOGIN', 'User logged in successfully', NULL, NULL, '*************', '2024-02-01 08:30:00'),
(2, 'MR_CREATE', 'Created Material Request MR-2024-0005', 'MaterialRequest', 5, '*************', '2024-02-01 09:15:00'),
(3, 'LOGIN', 'User logged in successfully', NULL, NULL, '*************', '2024-02-01 08:45:00'),
(3, 'EVALUATION_SUBMIT', 'Submitted technical evaluation for offer', 'Evaluation', 1, '*************', '2024-01-26 14:30:00'),
(4, 'LOGIN', 'User logged in successfully', NULL, NULL, '*************', '2024-02-01 07:30:00'),
(4, 'MR_APPROVE', 'Approved Material Request MR-2024-0001', 'MaterialRequest', 1, '*************', '2024-01-22 09:45:00'),
(5, 'LOGIN', 'User logged in successfully', NULL, NULL, '*************', '2024-02-01 08:00:00'),
(5, 'OFFER_ADD', 'Added offer from Industrial Electric Supply Co.', 'Offer', 1, '*************', '2024-01-23 16:20:00'),
(6, 'LOGIN', 'User logged in successfully', NULL, NULL, '*************', '2024-02-01 09:00:00'),
(7, 'LOGIN', 'User logged in successfully', NULL, NULL, '192.168.1.105', '2024-02-01 08:15:00'),
(7, 'EVALUATION_SUBMIT', 'Submitted commercial evaluation for offer', 'Evaluation', 2, '192.168.1.105', '2024-01-27 10:15:00');

-- Update MR assignments based on current stages
UPDATE material_requests SET assigned_to_id = 4 WHERE id = 1; -- MR_APPROVAL stage -> Section Head
UPDATE material_requests SET assigned_to_id = 5 WHERE id = 2; -- RECEIVING_OFFERS stage -> Material Coordinator  
UPDATE material_requests SET assigned_to_id = 3 WHERE id = 3; -- TECHNICAL_EVAL stage -> Technical Buyer
UPDATE material_requests SET assigned_to_id = 8 WHERE id = 4; -- CLOSING_OUT stage -> Warehouse Keeper
UPDATE material_requests SET assigned_to_id = NULL WHERE id = 5; -- DRAFT stage -> No assignment yet
