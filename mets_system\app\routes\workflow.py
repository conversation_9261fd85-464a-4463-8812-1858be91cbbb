from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from datetime import datetime
from app import db
from app.models.user import User, UserActivity
from app.models.material_request import MaterialRequest
from app.models.workflow import WorkflowTemplate, WorkflowStep, Offer, OfferItem, Evaluation
from app.services.ai_service import suggest_next_approver, predict_approval_outcome

workflow_bp = Blueprint('workflow', __name__)

@workflow_bp.route('/management')
@login_required
def management():
    """Workflow management dashboard"""
    if not current_user.is_admin:
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('main.dashboard'))
    
    # Get workflow templates
    templates = WorkflowTemplate.query.all()
    
    # Get workflow statistics
    workflow_stats = get_workflow_statistics()
    
    return render_template('workflow/management.html',
                         templates=templates,
                         workflow_stats=workflow_stats)

@workflow_bp.route('/template/create', methods=['GET', 'POST'])
@login_required
def create_template():
    """Create new workflow template"""
    if not current_user.is_admin:
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('main.dashboard'))
    
    if request.method == 'POST':
        try:
            # Create template
            template = WorkflowTemplate(
                name=request.form.get('name', '').strip(),
                description=request.form.get('description', '').strip(),
                is_default=bool(request.form.get('is_default'))
            )
            
            # If this is set as default, unset other defaults
            if template.is_default:
                WorkflowTemplate.query.filter_by(is_default=True).update({'is_default': False})
            
            db.session.add(template)
            db.session.flush()  # Get the ID
            
            # Add workflow steps
            steps_data = request.form.getlist('steps')
            for i, step_json in enumerate(steps_data):
                if step_json.strip():
                    import json
                    try:
                        step_data = json.loads(step_json)
                        step = WorkflowStep(
                            template_id=template.id,
                            step_number=i + 1,
                            stage_name=step_data.get('stage_name', ''),
                            display_name=step_data.get('display_name', ''),
                            description=step_data.get('description', ''),
                            required_role=step_data.get('required_role', ''),
                            is_parallel=bool(step_data.get('is_parallel', False)),
                            is_optional=bool(step_data.get('is_optional', False)),
                            sla_hours=int(step_data.get('sla_hours', 24))
                        )
                        db.session.add(step)
                    except (json.JSONDecodeError, ValueError) as e:
                        flash(f'Error processing step {i+1}: {str(e)}', 'error')
                        continue
            
            db.session.commit()
            
            # Log activity
            activity = UserActivity(
                user_id=current_user.id,
                action='WORKFLOW_TEMPLATE_CREATE',
                description=f'Created workflow template: {template.name}',
                entity_type='WorkflowTemplate',
                entity_id=template.id,
                ip_address=request.remote_addr
            )
            db.session.add(activity)
            db.session.commit()
            
            flash('Workflow template created successfully!', 'success')
            return redirect(url_for('workflow.management'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error creating workflow template: {str(e)}', 'error')
    
    # Get available roles for the form
    from config import Config
    roles = Config.USER_ROLES
    stages = Config.WORKFLOW_STAGES
    
    return render_template('workflow/create_template.html', roles=roles, stages=stages)

@workflow_bp.route('/offers/<int:mr_id>')
@login_required
def offers(mr_id):
    """Manage offers for Material Request"""
    mr = MaterialRequest.query.get_or_404(mr_id)
    
    # Check permissions
    if not can_manage_offers(mr, current_user):
        flash('You do not have permission to manage offers for this Material Request.', 'error')
        return redirect(url_for('mr.view', id=mr_id))
    
    offers = mr.offers.all()
    
    return render_template('workflow/offers.html', mr=mr, offers=offers)

@workflow_bp.route('/offers/<int:mr_id>/add', methods=['GET', 'POST'])
@login_required
def add_offer(mr_id):
    """Add new offer for Material Request"""
    mr = MaterialRequest.query.get_or_404(mr_id)
    
    # Check permissions
    if not can_manage_offers(mr, current_user):
        flash('You do not have permission to add offers for this Material Request.', 'error')
        return redirect(url_for('mr.view', id=mr_id))
    
    if request.method == 'POST':
        try:
            # Create offer
            offer = Offer(
                material_request_id=mr.id,
                supplier_name=request.form.get('supplier_name', '').strip(),
                supplier_contact=request.form.get('supplier_contact', '').strip(),
                supplier_email=request.form.get('supplier_email', '').strip(),
                supplier_phone=request.form.get('supplier_phone', '').strip(),
                offer_number=request.form.get('offer_number', '').strip(),
                total_amount=float(request.form.get('total_amount', 0)) if request.form.get('total_amount') else None,
                currency=request.form.get('currency', 'USD'),
                payment_terms=request.form.get('payment_terms', '').strip(),
                delivery_terms=request.form.get('delivery_terms', '').strip(),
                delivery_time=request.form.get('delivery_time', '').strip(),
                remarks=request.form.get('remarks', '').strip()
            )
            
            # Set dates
            if request.form.get('offer_date'):
                offer.offer_date = datetime.strptime(request.form.get('offer_date'), '%Y-%m-%d').date()
            
            if request.form.get('validity_date'):
                offer.validity_date = datetime.strptime(request.form.get('validity_date'), '%Y-%m-%d').date()
            
            db.session.add(offer)
            db.session.flush()  # Get the ID
            
            # Add offer items
            items_data = request.form.getlist('items')
            for i, item_json in enumerate(items_data):
                if item_json.strip():
                    import json
                    try:
                        item_data = json.loads(item_json)
                        item = OfferItem(
                            offer_id=offer.id,
                            description=item_data.get('description', ''),
                            specification=item_data.get('specification', ''),
                            quantity=float(item_data.get('quantity', 0)),
                            unit=item_data.get('unit', ''),
                            unit_price=float(item_data.get('unit_price', 0)),
                            total_price=float(item_data.get('total_price', 0)),
                            manufacturer=item_data.get('manufacturer', ''),
                            model_number=item_data.get('model_number', ''),
                            delivery_time=item_data.get('delivery_time', '')
                        )
                        db.session.add(item)
                    except (json.JSONDecodeError, ValueError) as e:
                        flash(f'Error processing item {i+1}: {str(e)}', 'error')
                        continue
            
            db.session.commit()
            
            # Log activity
            activity = UserActivity(
                user_id=current_user.id,
                action='OFFER_ADD',
                description=f'Added offer from {offer.supplier_name} for MR {mr.mr_number}',
                entity_type='Offer',
                entity_id=offer.id,
                ip_address=request.remote_addr
            )
            db.session.add(activity)
            db.session.commit()
            
            flash('Offer added successfully!', 'success')
            return redirect(url_for('workflow.offers', mr_id=mr_id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error adding offer: {str(e)}', 'error')
    
    return render_template('workflow/add_offer.html', mr=mr)

@workflow_bp.route('/evaluation/<int:mr_id>')
@login_required
def evaluation(mr_id):
    """Evaluation page for Material Request"""
    mr = MaterialRequest.query.get_or_404(mr_id)
    
    # Check permissions
    if not can_evaluate(mr, current_user):
        flash('You do not have permission to evaluate this Material Request.', 'error')
        return redirect(url_for('mr.view', id=mr_id))
    
    offers = mr.offers.all()
    evaluations = mr.evaluations.filter_by(evaluator_id=current_user.id).all()
    
    # Determine evaluation type based on current stage
    evaluation_type = 'TECHNICAL' if mr.current_stage == 'TECHNICAL_EVAL' else 'COMMERCIAL'
    
    return render_template('workflow/evaluation.html',
                         mr=mr,
                         offers=offers,
                         evaluations=evaluations,
                         evaluation_type=evaluation_type)

@workflow_bp.route('/evaluation/<int:offer_id>/submit', methods=['POST'])
@login_required
def submit_evaluation(offer_id):
    """Submit evaluation for an offer"""
    offer = Offer.query.get_or_404(offer_id)
    mr = offer.material_request
    
    # Check permissions
    if not can_evaluate(mr, current_user):
        flash('You do not have permission to evaluate this offer.', 'error')
        return redirect(url_for('mr.view', id=mr.id))
    
    try:
        # Determine evaluation type
        evaluation_type = 'TECHNICAL' if mr.current_stage == 'TECHNICAL_EVAL' else 'COMMERCIAL'
        
        # Check if evaluation already exists
        evaluation = Evaluation.query.filter_by(
            offer_id=offer.id,
            evaluator_id=current_user.id,
            evaluation_type=evaluation_type
        ).first()
        
        if not evaluation:
            evaluation = Evaluation(
                material_request_id=mr.id,
                offer_id=offer.id,
                evaluator_id=current_user.id,
                evaluation_type=evaluation_type
            )
            db.session.add(evaluation)
        
        # Update evaluation data
        evaluation.total_score = float(request.form.get('total_score', 0))
        evaluation.comments = request.form.get('comments', '').strip()
        evaluation.recommendation = request.form.get('recommendation', 'ACCEPT')
        evaluation.status = 'SUBMITTED'
        evaluation.submitted_at = datetime.utcnow()
        
        # Store detailed criteria scores
        criteria_scores = {}
        for key, value in request.form.items():
            if key.startswith('criteria_'):
                criteria_name = key.replace('criteria_', '')
                try:
                    criteria_scores[criteria_name] = float(value)
                except ValueError:
                    pass
        
        evaluation.criteria_scores = criteria_scores
        
        # Update offer scores
        if evaluation_type == 'TECHNICAL':
            offer.technical_score = evaluation.total_score
        else:
            offer.commercial_score = evaluation.total_score
        
        # Calculate overall score if both evaluations are complete
        if offer.technical_score and offer.commercial_score:
            offer.overall_score = (offer.technical_score + offer.commercial_score) / 2
        
        db.session.commit()
        
        # Log activity
        activity = UserActivity(
            user_id=current_user.id,
            action='EVALUATION_SUBMIT',
            description=f'Submitted {evaluation_type.lower()} evaluation for offer from {offer.supplier_name}',
            entity_type='Evaluation',
            entity_id=evaluation.id,
            ip_address=request.remote_addr
        )
        db.session.add(activity)
        db.session.commit()
        
        flash('Evaluation submitted successfully!', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'Error submitting evaluation: {str(e)}', 'error')
    
    return redirect(url_for('workflow.evaluation', mr_id=mr.id))

@workflow_bp.route('/ai-suggestions/<int:mr_id>')
@login_required
def ai_suggestions(mr_id):
    """Get AI suggestions for workflow"""
    mr = MaterialRequest.query.get_or_404(mr_id)
    
    suggestions = {}
    
    try:
        # Get next approver suggestion
        suggestions['next_approver'] = suggest_next_approver(mr)
        
        # Get approval prediction
        suggestions['approval_prediction'] = predict_approval_outcome(mr)
        
        # Get similar MRs for reference
        suggestions['similar_mrs'] = get_similar_mrs(mr)
        
    except Exception as e:
        suggestions['error'] = str(e)
    
    return jsonify(suggestions)

def get_workflow_statistics():
    """Get workflow statistics for dashboard"""
    from config import Config
    
    stats = {}
    
    # MRs by stage
    for stage in Config.WORKFLOW_STAGES:
        stats[stage] = MaterialRequest.query.filter_by(current_stage=stage).count()
    
    # Average processing time by stage
    # This would require more complex queries - simplified for now
    stats['avg_processing_time'] = {}
    
    # Bottlenecks (stages with most pending MRs)
    bottlenecks = db.session.query(
        MaterialRequest.current_stage,
        db.func.count(MaterialRequest.id).label('count')
    ).filter_by(status='PENDING').group_by(MaterialRequest.current_stage).all()
    
    stats['bottlenecks'] = [(stage, count) for stage, count in bottlenecks]
    
    return stats

def can_manage_offers(mr, user):
    """Check if user can manage offers for MR"""
    if user.is_admin:
        return True
    
    # Material coordinators and technical buyers can manage offers
    if user.role in ['MATERIAL_COORDINATOR', 'TECHNICAL_BUYER']:
        return True
    
    # Assigned user can manage offers
    if mr.assigned_to_id == user.id:
        return True
    
    return False

def can_evaluate(mr, user):
    """Check if user can evaluate offers for MR"""
    if user.is_admin:
        return True
    
    # Check if user can evaluate based on current stage and role
    if mr.current_stage == 'TECHNICAL_EVAL':
        return user.role in ['TECHNICAL_BUYER', 'SUPERVISOR', 'SECTION_HEAD_EXPAT', 'SECTION_HEAD_LOCAL']
    elif mr.current_stage == 'COMMERCIAL_EVAL':
        return user.role in ['COMMERCIAL_COMMITTEE', 'SECTION_HEAD_EXPAT', 'SECTION_HEAD_LOCAL']
    
    return False

def get_similar_mrs(mr):
    """Get similar MRs for AI suggestions"""
    # Simple similarity based on category and keywords
    similar = MaterialRequest.query.filter(
        MaterialRequest.id != mr.id,
        MaterialRequest.category == mr.category
    ).limit(5).all()
    
    return [{'id': m.id, 'mr_number': m.mr_number, 'title': m.title} for m in similar]
