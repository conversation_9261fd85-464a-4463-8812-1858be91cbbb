-- METS System Database Schema
-- Material Management System Database Structure

-- Create database
CREATE DATABASE IF NOT EXISTS mets_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE mets_system;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(80) NOT NULL UNIQUE,
    email VARCHAR(120) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    employee_id VARCHAR(20) UNIQUE,
    role VARCHAR(50) NOT NULL DEFAULT 'MATERIAL_SPECIALIST',
    department VARCHAR(100),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_admin BOOLEAN NOT NULL DEFAULT FALSE,
    phone VARCHAR(20),
    extension VARCHAR(10),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login DATETIME,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_active (is_active)
);

-- Groups table
CREATE TABLE groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- User Groups junction table
CREATE TABLE user_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    group_id INT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES groups(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_group (user_id, group_id)
);

-- Material Requests table
CREATE TABLE material_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    mr_number VARCHAR(50) NOT NULL UNIQUE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    priority VARCHAR(20) NOT NULL DEFAULT 'NORMAL',
    category VARCHAR(50),
    project_code VARCHAR(50),
    cost_center VARCHAR(50),
    status VARCHAR(50) NOT NULL DEFAULT 'DRAFT',
    current_stage VARCHAR(50) NOT NULL DEFAULT 'MR_CREATION',
    workflow_step INT NOT NULL DEFAULT 1,
    created_by_id INT NOT NULL,
    assigned_to_id INT,
    requested_date DATE,
    required_date DATE,
    approved_date DATETIME,
    completed_date DATETIME,
    estimated_cost DECIMAL(15,2),
    approved_budget DECIMAL(15,2),
    actual_cost DECIMAL(15,2),
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    justification TEXT,
    technical_specifications TEXT,
    delivery_location VARCHAR(200),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_id) REFERENCES users(id),
    FOREIGN KEY (assigned_to_id) REFERENCES users(id),
    INDEX idx_mr_number (mr_number),
    INDEX idx_status (status),
    INDEX idx_stage (current_stage),
    INDEX idx_created_by (created_by_id),
    INDEX idx_assigned_to (assigned_to_id),
    INDEX idx_created_at (created_at)
);

-- Material Request Items table
CREATE TABLE material_request_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    material_request_id INT NOT NULL,
    item_code VARCHAR(50),
    description TEXT NOT NULL,
    specification TEXT,
    quantity DECIMAL(10,2) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    unit_price DECIMAL(15,2),
    total_price DECIMAL(15,2),
    manufacturer VARCHAR(100),
    model_number VARCHAR(100),
    remarks TEXT,
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (material_request_id) REFERENCES material_requests(id) ON DELETE CASCADE,
    INDEX idx_mr_id (material_request_id)
);

-- Workflow Actions table
CREATE TABLE workflow_actions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    material_request_id INT NOT NULL,
    user_id INT NOT NULL,
    stage VARCHAR(50) NOT NULL,
    action VARCHAR(20) NOT NULL,
    comments TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (material_request_id) REFERENCES material_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_mr_id (material_request_id),
    INDEX idx_user_id (user_id),
    INDEX idx_stage (stage),
    INDEX idx_created_at (created_at)
);

-- Workflow Templates table
CREATE TABLE workflow_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Workflow Steps table
CREATE TABLE workflow_steps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_id INT NOT NULL,
    step_number INT NOT NULL,
    stage_name VARCHAR(50) NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    required_role VARCHAR(50),
    required_group_id INT,
    is_parallel BOOLEAN NOT NULL DEFAULT FALSE,
    is_optional BOOLEAN NOT NULL DEFAULT FALSE,
    sla_hours INT,
    conditions JSON,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES workflow_templates(id) ON DELETE CASCADE,
    FOREIGN KEY (required_group_id) REFERENCES groups(id),
    INDEX idx_template_id (template_id),
    INDEX idx_step_number (step_number)
);

-- Offers table
CREATE TABLE offers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    material_request_id INT NOT NULL,
    supplier_name VARCHAR(200) NOT NULL,
    supplier_contact VARCHAR(100),
    supplier_email VARCHAR(120),
    supplier_phone VARCHAR(20),
    offer_number VARCHAR(50),
    offer_date DATE,
    validity_date DATE,
    total_amount DECIMAL(15,2),
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    payment_terms VARCHAR(200),
    delivery_terms VARCHAR(200),
    delivery_time VARCHAR(100),
    status VARCHAR(50) NOT NULL DEFAULT 'RECEIVED',
    technical_score DECIMAL(5,2),
    commercial_score DECIMAL(5,2),
    overall_score DECIMAL(5,2),
    remarks TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (material_request_id) REFERENCES material_requests(id) ON DELETE CASCADE,
    INDEX idx_mr_id (material_request_id),
    INDEX idx_supplier (supplier_name),
    INDEX idx_status (status)
);

-- Offer Items table
CREATE TABLE offer_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    offer_id INT NOT NULL,
    material_request_item_id INT,
    description TEXT NOT NULL,
    specification TEXT,
    quantity DECIMAL(10,2) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    unit_price DECIMAL(15,2) NOT NULL,
    total_price DECIMAL(15,2) NOT NULL,
    manufacturer VARCHAR(100),
    model_number VARCHAR(100),
    delivery_time VARCHAR(100),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    FOREIGN KEY (material_request_item_id) REFERENCES material_request_items(id),
    INDEX idx_offer_id (offer_id)
);

-- Evaluations table
CREATE TABLE evaluations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    material_request_id INT NOT NULL,
    offer_id INT NOT NULL,
    evaluator_id INT NOT NULL,
    evaluation_type VARCHAR(20) NOT NULL,
    criteria_scores JSON,
    total_score DECIMAL(5,2),
    comments TEXT,
    recommendation VARCHAR(20),
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    submitted_at DATETIME,
    FOREIGN KEY (material_request_id) REFERENCES material_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    FOREIGN KEY (evaluator_id) REFERENCES users(id),
    INDEX idx_mr_id (material_request_id),
    INDEX idx_offer_id (offer_id),
    INDEX idx_evaluator_id (evaluator_id),
    INDEX idx_type (evaluation_type)
);

-- Attachments table
CREATE TABLE attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    material_request_id INT NOT NULL,
    uploaded_by_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100),
    description TEXT,
    category VARCHAR(50),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (material_request_id) REFERENCES material_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by_id) REFERENCES users(id),
    INDEX idx_mr_id (material_request_id),
    INDEX idx_uploaded_by (uploaded_by_id),
    INDEX idx_category (category)
);

-- Templates table
CREATE TABLE templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    template_data JSON NOT NULL,
    created_by_id INT NOT NULL,
    is_public BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    usage_count INT NOT NULL DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_id) REFERENCES users(id),
    INDEX idx_created_by (created_by_id),
    INDEX idx_category (category),
    INDEX idx_public (is_public)
);

-- User Activities table
CREATE TABLE user_activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    entity_type VARCHAR(50),
    entity_id INT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_created_at (created_at)
);

-- Create indexes for better performance
CREATE INDEX idx_mr_status_stage ON material_requests(status, current_stage);
CREATE INDEX idx_mr_dates ON material_requests(created_at, updated_at);
CREATE INDEX idx_workflow_mr_user ON workflow_actions(material_request_id, user_id);
CREATE INDEX idx_offers_mr_status ON offers(material_request_id, status);
CREATE INDEX idx_evaluations_mr_type ON evaluations(material_request_id, evaluation_type);

-- Create full-text search indexes
ALTER TABLE material_requests ADD FULLTEXT(title, description);
ALTER TABLE material_request_items ADD FULLTEXT(description, specification);

-- Views for common queries
CREATE VIEW v_pending_approvals AS
SELECT 
    mr.id,
    mr.mr_number,
    mr.title,
    mr.priority,
    mr.current_stage,
    mr.created_at,
    mr.updated_at,
    creator.full_name as created_by_name,
    assignee.full_name as assigned_to_name,
    assignee.email as assigned_to_email
FROM material_requests mr
LEFT JOIN users creator ON mr.created_by_id = creator.id
LEFT JOIN users assignee ON mr.assigned_to_id = assignee.id
WHERE mr.status = 'PENDING';

CREATE VIEW v_mr_summary AS
SELECT 
    mr.id,
    mr.mr_number,
    mr.title,
    mr.status,
    mr.current_stage,
    mr.priority,
    mr.estimated_cost,
    mr.currency,
    mr.created_at,
    creator.full_name as created_by,
    assignee.full_name as assigned_to,
    COUNT(items.id) as items_count,
    COUNT(attachments.id) as attachments_count,
    COUNT(offers.id) as offers_count
FROM material_requests mr
LEFT JOIN users creator ON mr.created_by_id = creator.id
LEFT JOIN users assignee ON mr.assigned_to_id = assignee.id
LEFT JOIN material_request_items items ON mr.id = items.material_request_id
LEFT JOIN attachments ON mr.id = attachments.material_request_id AND attachments.is_active = TRUE
LEFT JOIN offers ON mr.id = offers.material_request_id
GROUP BY mr.id;

-- Stored procedures for common operations
DELIMITER //

CREATE PROCEDURE GetUserWorkload(IN user_id INT)
BEGIN
    SELECT 
        current_stage,
        COUNT(*) as count,
        AVG(DATEDIFF(NOW(), updated_at)) as avg_days_pending
    FROM material_requests 
    WHERE assigned_to_id = user_id AND status = 'PENDING'
    GROUP BY current_stage;
END //

CREATE PROCEDURE GetWorkflowStatistics()
BEGIN
    SELECT 
        current_stage,
        status,
        COUNT(*) as count,
        AVG(DATEDIFF(NOW(), created_at)) as avg_age_days
    FROM material_requests 
    GROUP BY current_stage, status
    ORDER BY current_stage, status;
END //

CREATE PROCEDURE GetOverdueMRs(IN days_threshold INT)
BEGIN
    SELECT 
        mr.id,
        mr.mr_number,
        mr.title,
        mr.current_stage,
        mr.assigned_to_id,
        assignee.full_name as assigned_to_name,
        assignee.email as assigned_to_email,
        DATEDIFF(NOW(), mr.updated_at) as days_overdue
    FROM material_requests mr
    LEFT JOIN users assignee ON mr.assigned_to_id = assignee.id
    WHERE mr.status = 'PENDING' 
    AND DATEDIFF(NOW(), mr.updated_at) > days_threshold
    ORDER BY days_overdue DESC;
END //

DELIMITER ;

-- Triggers for audit trail
DELIMITER //

CREATE TRIGGER tr_mr_status_change 
AFTER UPDATE ON material_requests
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status OR OLD.current_stage != NEW.current_stage THEN
        INSERT INTO user_activities (
            user_id, 
            action, 
            description, 
            entity_type, 
            entity_id
        ) VALUES (
            NEW.assigned_to_id,
            'MR_STATUS_CHANGE',
            CONCAT('Status changed from ', OLD.status, ' to ', NEW.status, 
                   ', Stage changed from ', OLD.current_stage, ' to ', NEW.current_stage),
            'MaterialRequest',
            NEW.id
        );
    END IF;
END //

DELIMITER ;

-- Insert default data
INSERT INTO users (username, email, password_hash, first_name, last_name, role, is_admin, employee_id) VALUES
('admin', '<EMAIL>', 'scrypt:32768:8:1$salt$hash', 'System', 'Administrator', 'ADMIN', TRUE, 'EMP001');

INSERT INTO groups (name, description) VALUES
('Material Specialists', 'Material request creators and specialists'),
('Technical Buyers', 'Technical evaluation and purchasing team'),
('Section Heads', 'Department section heads'),
('Maintenance Managers', 'Maintenance department managers'),
('Commercial Committee', 'Commercial evaluation committee'),
('Warehouse Team', 'Warehouse and inventory management'),
('Supervisors', 'Department supervisors');

INSERT INTO workflow_templates (name, description, is_default) VALUES
('Standard Material Request Workflow', 'Standard 8-stage material request approval workflow', TRUE);

-- Insert workflow steps for the default template
INSERT INTO workflow_steps (template_id, step_number, stage_name, display_name, description, required_role, sla_hours) VALUES
(1, 1, 'MR_CREATION', 'MR Creation', 'Material Request creation by specialists', 'MATERIAL_SPECIALIST', 24),
(1, 2, 'MR_APPROVAL', 'MR Approval', 'Multi-level approval process', 'SECTION_HEAD_EXPAT', 48),
(1, 3, 'RECEIVING_OFFERS', 'Receiving Offers', 'Collecting vendor offers and quotations', 'MATERIAL_COORDINATOR', 120),
(1, 4, 'TECHNICAL_EVAL', 'Technical Evaluation', 'Technical evaluation of offers', 'TECHNICAL_BUYER', 72),
(1, 5, 'COMMERCIAL_EVAL', 'Commercial Evaluation', 'Commercial evaluation and comparison', 'COMMERCIAL_COMMITTEE', 48),
(1, 6, 'MANAGEMENT_APPR', 'Management Approval', 'Final management approval', 'MAINTENANCE_MANAGER', 24),
(1, 7, 'PURCHASING_PROCESS', 'Purchasing Process', 'Purchase order creation and processing', 'MATERIAL_COORDINATOR', 48),
(1, 8, 'CLOSING_OUT', 'Closing Out', 'Delivery tracking and completion', 'WAREHOUSE_KEEPER', 24);

-- Grant permissions (adjust as needed for your MySQL setup)
-- GRANT ALL PRIVILEGES ON mets_system.* TO 'mets_user'@'localhost' IDENTIFIED BY 'mets_password';
-- FLUSH PRIVILEGES;
