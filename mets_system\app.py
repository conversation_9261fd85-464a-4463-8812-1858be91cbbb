#!/usr/bin/env python3
"""
METS (Material Management System) - Main Application Entry Point
"""

import os
from flask import Flask
from app import create_app, db
from app.models.user import User, Group, UserGroup, UserActivity
from app.models.material_request import MaterialRequest, MaterialRequestItem
from app.models.workflow import WorkflowAction, WorkflowTemplate, WorkflowStep, Offer, OfferItem, Evaluation
from app.models.attachment import Attachment, Template

# Create Flask application
app = create_app()

@app.shell_context_processor
def make_shell_context():
    """Make database models available in Flask shell"""
    return {
        'db': db,
        'User': User,
        'Group': Group,
        'UserGroup': UserGroup,
        'UserActivity': UserActivity,
        'MaterialRequest': MaterialRequest,
        'MaterialRequestItem': MaterialRequestItem,
        'WorkflowAction': WorkflowAction,
        'WorkflowTemplate': WorkflowTemplate,
        'WorkflowStep': WorkflowStep,
        'Offer': Offer,
        'OfferItem': OfferItem,
        'Evaluation': Evaluation,
        'Attachment': Attachment,
        'Template': Template
    }

@app.cli.command()
def init_db():
    """Initialize the database with tables and sample data"""
    print("Creating database tables...")
    db.create_all()
    
    print("Creating default admin user...")
    create_default_admin()
    
    print("Creating default groups...")
    create_default_groups()
    
    print("Creating workflow templates...")
    create_default_workflow_templates()
    
    print("Database initialization completed!")

def create_default_admin():
    """Create default admin user"""
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            first_name='System',
            last_name='Administrator',
            role='ADMIN',
            department='IT',
            is_admin=True,
            employee_id='EMP001'
        )
        admin.set_password('admin123')
        db.session.add(admin)
        db.session.commit()
        print(f"Created admin user: {admin.username}")

def create_default_groups():
    """Create default user groups"""
    default_groups = [
        {'name': 'Material Specialists', 'description': 'Material request creators and specialists'},
        {'name': 'Technical Buyers', 'description': 'Technical evaluation and purchasing team'},
        {'name': 'Section Heads', 'description': 'Department section heads'},
        {'name': 'Maintenance Managers', 'description': 'Maintenance department managers'},
        {'name': 'Commercial Committee', 'description': 'Commercial evaluation committee'},
        {'name': 'Warehouse Team', 'description': 'Warehouse and inventory management'},
        {'name': 'Supervisors', 'description': 'Department supervisors'}
    ]
    
    for group_data in default_groups:
        group = Group.query.filter_by(name=group_data['name']).first()
        if not group:
            group = Group(**group_data)
            db.session.add(group)
    
    db.session.commit()
    print("Created default groups")

def create_default_workflow_templates():
    """Create default workflow template based on the provided diagram"""
    template = WorkflowTemplate.query.filter_by(name='Standard Material Request Workflow').first()
    if not template:
        template = WorkflowTemplate(
            name='Standard Material Request Workflow',
            description='Standard 8-stage material request approval workflow',
            is_default=True
        )
        db.session.add(template)
        db.session.flush()  # Get the ID
        
        # Create workflow steps based on the diagram
        workflow_steps = [
            {
                'step_number': 1,
                'stage_name': 'MR_CREATION',
                'display_name': 'MR Creation',
                'description': 'Material Request creation by specialists',
                'required_role': 'MATERIAL_SPECIALIST',
                'sla_hours': 24
            },
            {
                'step_number': 2,
                'stage_name': 'MR_APPROVAL',
                'display_name': 'MR Approval',
                'description': 'Multi-level approval process',
                'required_role': 'SECTION_HEAD_EXPAT',
                'sla_hours': 48
            },
            {
                'step_number': 3,
                'stage_name': 'RECEIVING_OFFERS',
                'display_name': 'Receiving Offers',
                'description': 'Collecting vendor offers and quotations',
                'required_role': 'MATERIAL_COORDINATOR',
                'sla_hours': 120  # 5 days
            },
            {
                'step_number': 4,
                'stage_name': 'TECHNICAL_EVAL',
                'display_name': 'Technical Evaluation',
                'description': 'Technical evaluation of offers',
                'required_role': 'TECHNICAL_BUYER',
                'sla_hours': 72  # 3 days
            },
            {
                'step_number': 5,
                'stage_name': 'COMMERCIAL_EVAL',
                'display_name': 'Commercial Evaluation',
                'description': 'Commercial evaluation and comparison',
                'required_role': 'COMMERCIAL_COMMITTEE',
                'sla_hours': 48
            },
            {
                'step_number': 6,
                'stage_name': 'MANAGEMENT_APPR',
                'display_name': 'Management Approval',
                'description': 'Final management approval',
                'required_role': 'MAINTENANCE_MANAGER',
                'sla_hours': 24
            },
            {
                'step_number': 7,
                'stage_name': 'PURCHASING_PROCESS',
                'display_name': 'Purchasing Process',
                'description': 'Purchase order creation and processing',
                'required_role': 'MATERIAL_COORDINATOR',
                'sla_hours': 48
            },
            {
                'step_number': 8,
                'stage_name': 'CLOSING_OUT',
                'display_name': 'Closing Out',
                'description': 'Delivery tracking and completion',
                'required_role': 'WAREHOUSE_KEEPER',
                'sla_hours': 24
            }
        ]
        
        for step_data in workflow_steps:
            step = WorkflowStep(template_id=template.id, **step_data)
            db.session.add(step)
        
        db.session.commit()
        print("Created default workflow template")

if __name__ == '__main__':
    # Run the application
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    print(f"""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    METS System Starting                      ║
    ║              Material Management System                      ║
    ║                                                              ║
    ║  Server: http://localhost:{port}                                ║
    ║  Debug Mode: {debug}                                           ║
    ║                                                              ║
    ║  Default Admin Login:                                        ║
    ║  Username: admin                                             ║
    ║  Password: admin123                                          ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    app.run(host='0.0.0.0', port=port, debug=debug)
