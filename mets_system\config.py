import os
from datetime import timedelta

class Config:
    # Basic Flask configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'mets-secret-key-2024'
    
    # Database configuration for XAMPP MySQL
    MYSQL_HOST = os.environ.get('MY<PERSON>QL_HOST') or 'localhost'
    MYSQL_PORT = int(os.environ.get('MYSQL_PORT') or 3306)
    MYSQL_USER = os.environ.get('MYSQL_USER') or 'root'
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or ''
    MYSQL_DATABASE = os.environ.get('MYSQL_DATABASE') or 'mets_system'
    
    # SQLAlchemy configuration
    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_recycle': 300,
        'pool_pre_ping': True
    }
    
    # File upload configuration
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'static', 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'xls', 'xlsx'}
    
    # Session configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)
    
    # Email configuration
    MAIL_SERVER = os.environ.get('MAIL_SERVER') or 'smtp.gmail.com'
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER') or '<EMAIL>'
    
    # AI Configuration
    AI_ENABLED = os.environ.get('AI_ENABLED', 'true').lower() in ['true', 'on', '1']
    
    # Pagination
    ITEMS_PER_PAGE = 20
    
    # Workflow Configuration
    WORKFLOW_STAGES = [
        'MR_CREATION',
        'MR_APPROVAL', 
        'RECEIVING_OFFERS',
        'TECHNICAL_EVAL',
        'COMMERCIAL_EVAL',
        'MANAGEMENT_APPR',
        'PURCHASING_PROCESS',
        'CLOSING_OUT'
    ]
    
    # User Roles based on workflow diagram
    USER_ROLES = [
        'MATERIAL_SPECIALIST',
        'TECHNICAL_BUYER', 
        'MATERIAL_COORDINATOR',
        'SUPERVISOR',
        'SECTION_HEAD_EXPAT',
        'SECTION_HEAD_LOCAL',
        'WAREHOUSE_KEEPER',
        'TECH_COMMITTEE',
        'COMMERCIAL_COMMITTEE',
        'MAINTENANCE_MANAGER',
        'ADMIN'
    ]

class DevelopmentConfig(Config):
    DEBUG = True
    
class ProductionConfig(Config):
    DEBUG = False
    
class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
