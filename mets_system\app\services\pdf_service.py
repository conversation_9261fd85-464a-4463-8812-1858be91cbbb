"""
PDF Generation Service for METS System
Generates PDF reports and documents
"""

from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from datetime import datetime
import os
import tempfile

def generate_mr_pdf(mr):
    """Generate PDF for Material Request"""
    try:
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_file.close()
        
        # Create PDF document
        doc = SimpleDocTemplate(
            temp_file.name,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        # Build content
        story = []
        styles = getSampleStyleSheet()
        
        # Add custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )
        
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            textColor=colors.darkblue
        )
        
        # Title
        story.append(Paragraph("MATERIAL REQUEST", title_style))
        story.append(Spacer(1, 12))
        
        # MR Header Information
        header_data = [
            ['MR Number:', mr.mr_number, 'Status:', mr.status],
            ['Title:', mr.title, 'Priority:', mr.priority],
            ['Created By:', mr.creator.full_name if mr.creator else 'N/A', 'Created Date:', mr.created_at.strftime('%Y-%m-%d') if mr.created_at else 'N/A'],
            ['Current Stage:', mr.current_stage.replace('_', ' ').title(), 'Assigned To:', mr.assignee.full_name if mr.assignee else 'N/A']
        ]
        
        if mr.estimated_cost:
            header_data.append(['Estimated Cost:', f"{mr.currency} {mr.estimated_cost:,.2f}", '', ''])
        
        header_table = Table(header_data, colWidths=[1.5*inch, 2.5*inch, 1.5*inch, 2.5*inch])
        header_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('BACKGROUND', (2, 0), (2, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (2, 0), (2, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))
        
        story.append(header_table)
        story.append(Spacer(1, 20))
        
        # Description
        if mr.description:
            story.append(Paragraph("Description", heading_style))
            story.append(Paragraph(mr.description, styles['Normal']))
            story.append(Spacer(1, 12))
        
        # Justification
        if mr.justification:
            story.append(Paragraph("Justification", heading_style))
            story.append(Paragraph(mr.justification, styles['Normal']))
            story.append(Spacer(1, 12))
        
        # Technical Specifications
        if mr.technical_specifications:
            story.append(Paragraph("Technical Specifications", heading_style))
            story.append(Paragraph(mr.technical_specifications, styles['Normal']))
            story.append(Spacer(1, 12))
        
        # Items Table
        story.append(Paragraph("Requested Items", heading_style))
        
        items_data = [['#', 'Description', 'Specification', 'Qty', 'Unit', 'Unit Price', 'Total']]
        
        total_amount = 0
        for i, item in enumerate(mr.items, 1):
            unit_price = f"{mr.currency} {item.unit_price:,.2f}" if item.unit_price else 'TBD'
            total_price = f"{mr.currency} {item.total_price:,.2f}" if item.total_price else 'TBD'
            
            if item.total_price:
                total_amount += float(item.total_price)
            
            items_data.append([
                str(i),
                item.description[:50] + '...' if len(item.description) > 50 else item.description,
                item.specification[:30] + '...' if item.specification and len(item.specification) > 30 else (item.specification or ''),
                str(item.quantity),
                item.unit,
                unit_price,
                total_price
            ])
        
        # Add total row
        if total_amount > 0:
            items_data.append(['', '', '', '', '', 'TOTAL:', f"{mr.currency} {total_amount:,.2f}"])
        
        items_table = Table(items_data, colWidths=[0.5*inch, 2.5*inch, 1.5*inch, 0.7*inch, 0.7*inch, 1*inch, 1*inch])
        items_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('ALIGN', (1, 1), (2, -1), 'LEFT'),  # Left align description and specification
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))
        
        # Highlight total row if exists
        if total_amount > 0:
            items_table.setStyle(TableStyle([
                ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
                ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ]))
        
        story.append(items_table)
        story.append(Spacer(1, 20))
        
        # Workflow History
        workflow_actions = mr.workflow_actions.order_by('created_at').all()
        if workflow_actions:
            story.append(Paragraph("Workflow History", heading_style))
            
            workflow_data = [['Date', 'Stage', 'Action', 'User', 'Comments']]
            
            for action in workflow_actions:
                workflow_data.append([
                    action.created_at.strftime('%Y-%m-%d %H:%M') if action.created_at else '',
                    action.stage.replace('_', ' ').title(),
                    action.action,
                    action.user.full_name if action.user else 'N/A',
                    action.comments[:50] + '...' if action.comments and len(action.comments) > 50 else (action.comments or '')
                ])
            
            workflow_table = Table(workflow_data, colWidths=[1.2*inch, 1.5*inch, 1*inch, 1.5*inch, 2.8*inch])
            workflow_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ]))
            
            story.append(workflow_table)
            story.append(Spacer(1, 20))
        
        # Attachments
        attachments = mr.attachments.filter_by(is_active=True).all()
        if attachments:
            story.append(Paragraph("Attachments", heading_style))
            
            attachment_data = [['File Name', 'Size', 'Uploaded By', 'Upload Date']]
            
            for attachment in attachments:
                attachment_data.append([
                    attachment.original_filename,
                    attachment.file_size_formatted,
                    attachment.uploaded_by.full_name if attachment.uploaded_by else 'N/A',
                    attachment.created_at.strftime('%Y-%m-%d') if attachment.created_at else ''
                ])
            
            attachment_table = Table(attachment_data, colWidths=[3*inch, 1*inch, 2*inch, 1.5*inch])
            attachment_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ]))
            
            story.append(attachment_table)
        
        # Footer
        story.append(Spacer(1, 30))
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=8,
            alignment=TA_CENTER,
            textColor=colors.grey
        )
        story.append(Paragraph(f"Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} by METS System", footer_style))
        
        # Build PDF
        doc.build(story)
        
        return temp_file.name
        
    except Exception as e:
        print(f"Error generating PDF: {str(e)}")
        raise e

def generate_evaluation_pdf(mr, offers, evaluations):
    """Generate PDF for evaluation report"""
    try:
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_file.close()
        
        # Create PDF document
        doc = SimpleDocTemplate(
            temp_file.name,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        story = []
        styles = getSampleStyleSheet()
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )
        
        story.append(Paragraph("EVALUATION REPORT", title_style))
        story.append(Spacer(1, 12))
        
        # MR Information
        mr_info = f"""
        <b>MR Number:</b> {mr.mr_number}<br/>
        <b>Title:</b> {mr.title}<br/>
        <b>Current Stage:</b> {mr.current_stage.replace('_', ' ').title()}<br/>
        <b>Evaluation Date:</b> {datetime.now().strftime('%Y-%m-%d')}
        """
        story.append(Paragraph(mr_info, styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Offers Summary
        if offers:
            story.append(Paragraph("Offers Summary", styles['Heading2']))
            
            offers_data = [['Supplier', 'Offer Amount', 'Technical Score', 'Commercial Score', 'Overall Score', 'Status']]
            
            for offer in offers:
                offers_data.append([
                    offer.supplier_name,
                    f"{offer.currency} {offer.total_amount:,.2f}" if offer.total_amount else 'TBD',
                    f"{offer.technical_score:.1f}" if offer.technical_score else 'N/A',
                    f"{offer.commercial_score:.1f}" if offer.commercial_score else 'N/A',
                    f"{offer.overall_score:.1f}" if offer.overall_score else 'N/A',
                    offer.status
                ])
            
            offers_table = Table(offers_data, colWidths=[1.5*inch, 1.2*inch, 1*inch, 1*inch, 1*inch, 1*inch])
            offers_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ]))
            
            story.append(offers_table)
            story.append(Spacer(1, 20))
        
        # Detailed Evaluations
        if evaluations:
            story.append(Paragraph("Detailed Evaluations", styles['Heading2']))
            
            for evaluation in evaluations:
                eval_info = f"""
                <b>Evaluator:</b> {evaluation.evaluator.full_name}<br/>
                <b>Evaluation Type:</b> {evaluation.evaluation_type}<br/>
                <b>Total Score:</b> {evaluation.total_score:.1f}<br/>
                <b>Recommendation:</b> {evaluation.recommendation}<br/>
                <b>Date:</b> {evaluation.submitted_at.strftime('%Y-%m-%d %H:%M') if evaluation.submitted_at else 'N/A'}
                """
                
                story.append(Paragraph(eval_info, styles['Normal']))
                
                if evaluation.comments:
                    story.append(Paragraph(f"<b>Comments:</b> {evaluation.comments}", styles['Normal']))
                
                story.append(Spacer(1, 12))
        
        # Build PDF
        doc.build(story)
        
        return temp_file.name
        
    except Exception as e:
        print(f"Error generating evaluation PDF: {str(e)}")
        raise e

def generate_workflow_report_pdf(workflow_stats):
    """Generate PDF for workflow statistics report"""
    try:
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_file.close()
        
        # Create PDF document
        doc = SimpleDocTemplate(
            temp_file.name,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        story = []
        styles = getSampleStyleSheet()
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )
        
        story.append(Paragraph("WORKFLOW STATISTICS REPORT", title_style))
        story.append(Spacer(1, 12))
        
        # Report date
        story.append(Paragraph(f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Workflow stages statistics
        story.append(Paragraph("Material Requests by Workflow Stage", styles['Heading2']))
        
        stage_data = [['Workflow Stage', 'Count', 'Percentage']]
        total_mrs = sum(workflow_stats.values())
        
        for stage, count in workflow_stats.items():
            percentage = (count / total_mrs * 100) if total_mrs > 0 else 0
            stage_data.append([
                stage.replace('_', ' ').title(),
                str(count),
                f"{percentage:.1f}%"
            ])
        
        stage_table = Table(stage_data, colWidths=[3*inch, 1*inch, 1*inch])
        stage_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('ALIGN', (1, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))
        
        story.append(stage_table)
        story.append(Spacer(1, 20))
        
        # Summary
        story.append(Paragraph("Summary", styles['Heading2']))
        summary_text = f"""
        Total Material Requests: {total_mrs}<br/>
        Most Active Stage: {max(workflow_stats, key=workflow_stats.get).replace('_', ' ').title() if workflow_stats else 'N/A'}<br/>
        Report Period: All Time
        """
        story.append(Paragraph(summary_text, styles['Normal']))
        
        # Build PDF
        doc.build(story)
        
        return temp_file.name
        
    except Exception as e:
        print(f"Error generating workflow report PDF: {str(e)}")
        raise e
