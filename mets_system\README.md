# METS - Material Management System

A comprehensive Material Request Management System built with Flask, designed for industrial maintenance and procurement workflows.

## Features

### Core Functionality
- **Material Request Management**: Create, track, and manage material requests through an 8-stage approval workflow
- **Multi-level User Permissions**: Role-based access control with different user types (Material Specialists, Technical Buyers, Section Heads, etc.)
- **Workflow Management**: Automated workflow routing based on user roles and request types
- **Offer Management**: Collect and compare vendor offers with technical and commercial evaluation
- **Document Management**: File attachments with support for specifications, drawings, and quotes
- **Reporting & Analytics**: Comprehensive reports and dashboard analytics

### Advanced Features
- **AI-Assisted Workflow**: Machine learning predictions for approval outcomes and next approver suggestions
- **Email Notifications**: Automated email notifications for workflow actions
- **Template System**: Reusable templates for common material requests
- **Export Capabilities**: PDF and Excel export for reports and documentation
- **Audit Trail**: Complete activity logging and user action tracking
- **Dashboard Analytics**: Real-time statistics and workflow bottleneck analysis

## System Architecture

### Workflow Stages
1. **MR Creation** - Material Request creation by specialists
2. **MR Approval** - Multi-level approval process
3. **Receiving Offers** - Vendor offer collection
4. **Technical Evaluation** - Technical assessment of offers
5. **Commercial Evaluation** - Commercial terms evaluation
6. **Management Approval** - Final management sign-off
7. **Purchasing Process** - Purchase order creation
8. **Closing Out** - Delivery tracking and completion

### User Roles
- **Material Specialist** - Creates and manages material requests
- **Technical Buyer** - Handles procurement and technical evaluation
- **Material Coordinator** - Coordinates offer collection and purchasing
- **Section Head (Expat/Local)** - Provides departmental approvals
- **Maintenance Manager** - Final management approval
- **Commercial Committee** - Commercial evaluation and terms
- **Warehouse Keeper** - Delivery tracking and inventory
- **Supervisor** - Departmental oversight
- **Admin** - System administration

## Technology Stack

### Backend
- **Flask 2.3.3** - Web framework
- **SQLAlchemy 2.0.21** - ORM and database management
- **MySQL** - Primary database (via PyMySQL)
- **Flask-Login** - User authentication
- **Flask-Mail** - Email notifications
- **Flask-Migrate** - Database migrations

### AI & Analytics
- **scikit-learn** - Machine learning for workflow predictions
- **pandas** - Data analysis and processing
- **numpy** - Numerical computations

### Document Generation
- **ReportLab** - PDF generation
- **openpyxl** - Excel export functionality
- **Pillow** - Image processing

### Frontend
- **Jinja2** - Template engine
- **Bootstrap** - Responsive UI framework
- **JavaScript** - Interactive features

## Installation

### Prerequisites
- Python 3.8+
- MySQL 5.7+ or MariaDB 10.3+
- XAMPP (recommended for local development)

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mets_system
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Database Setup**
   - Start XAMPP and ensure MySQL is running
   - Create database using the provided schema:
   ```bash
   mysql -u root -p < database/schema.sql
   ```
   - Load sample data (optional):
   ```bash
   mysql -u root -p mets_system < database/sample_data.sql
   ```

5. **Environment Configuration**
   Create a `.env` file in the root directory:
   ```env
   FLASK_APP=app.py
   FLASK_ENV=development
   SECRET_KEY=your-secret-key-here
   MYSQL_HOST=localhost
   MYSQL_PORT=3306
   MYSQL_USER=root
   MYSQL_PASSWORD=
   MYSQL_DATABASE=mets_system
   MAIL_SERVER=smtp.gmail.com
   MAIL_PORT=587
   MAIL_USE_TLS=true
   MAIL_USERNAME=<EMAIL>
   MAIL_PASSWORD=your-app-password
   ```

6. **Initialize Database**
   ```bash
   python app.py init_db
   ```

7. **Run the Application**
   ```bash
   python app.py
   ```

The application will be available at `http://localhost:5000`

### Default Login
- **Username**: admin
- **Password**: admin123

## Configuration

### Database Configuration
The system is configured to work with MySQL via XAMPP by default. Update the database settings in `config.py` or use environment variables.

### Email Configuration
Configure SMTP settings for email notifications:
- Gmail: Use app-specific passwords
- Corporate SMTP: Update server settings accordingly

### File Upload Configuration
- Default upload directory: `app/static/uploads`
- Maximum file size: 16MB
- Allowed extensions: txt, pdf, png, jpg, jpeg, gif, doc, docx, xls, xlsx

## Usage

### Creating Material Requests
1. Log in with appropriate credentials
2. Navigate to "Create MR" from the dashboard
3. Fill in request details and add items
4. Attach supporting documents
5. Submit for approval or save as draft

### Approval Workflow
1. Assigned approvers receive email notifications
2. Review request details and attachments
3. Approve, reject, or return with comments
4. System automatically routes to next stage

### Offer Management
1. Material coordinators collect vendor offers
2. Enter offer details and pricing
3. Technical and commercial evaluators assess offers
4. Generate comparison reports

### Reporting
- **Dashboard**: Real-time statistics and pending actions
- **Material Register**: Searchable database of all requests
- **Workflow Reports**: Bottleneck analysis and performance metrics
- **Export Options**: PDF and Excel formats available

## API Endpoints

### Material Requests
- `GET /api/mr/<id>` - Get MR details
- `POST /api/mr/<id>/items` - Add items to MR
- `PUT /api/mr/items/<id>` - Update MR item
- `DELETE /api/mr/items/<id>` - Delete MR item

### Attachments
- `GET /api/attachments/<id>/download` - Download file
- `GET /api/attachments/<id>/preview` - Preview image
- `DELETE /api/attachments/<id>` - Delete attachment

### Users & Search
- `GET /api/users/search` - Search users
- `GET /api/dashboard/stats` - Dashboard statistics

### Export
- `GET /api/export/mr/<id>` - Export MR as PDF
- `GET /api/export/register` - Export register as Excel

## AI Features

### Workflow Predictions
- **Approval Outcome Prediction**: ML model predicts likelihood of approval
- **Next Approver Suggestion**: AI suggests optimal approver based on historical data
- **Processing Time Estimation**: Predicts completion timeline
- **Bottleneck Analysis**: Identifies workflow inefficiencies

### Training Models
```bash
python -c "from app.services.ai_service import train_ai_models; train_ai_models()"
```

## Development

### Project Structure
```
mets_system/
├── app/
│   ├── __init__.py          # Flask app factory
│   ├── models/              # Database models
│   ├── routes/              # Route blueprints
│   ├── services/            # Business logic services
│   ├── static/              # Static files (CSS, JS, uploads)
│   └── templates/           # Jinja2 templates
├── database/
│   ├── schema.sql           # Database schema
│   └── sample_data.sql      # Sample data
├── config.py                # Configuration settings
├── app.py                   # Main application entry point
├── requirements.txt         # Python dependencies
└── README.md               # This file
```

### Running Tests
```bash
pytest
```

### Code Coverage
```bash
coverage run -m pytest
coverage report
coverage html
```

## Deployment

### Production Setup
1. Use a production WSGI server (Gunicorn recommended)
2. Configure reverse proxy (Nginx)
3. Set up SSL certificates
4. Use production database (MySQL/PostgreSQL)
5. Configure proper logging
6. Set up monitoring (optional: Sentry)

### Docker Deployment
```bash
# Build image
docker build -t mets-system .

# Run container
docker run -p 5000:5000 -e FLASK_ENV=production mets-system
```

### Environment Variables for Production
```env
FLASK_ENV=production
SECRET_KEY=strong-production-secret-key
MYSQL_HOST=production-db-host
MYSQL_PASSWORD=secure-db-password
MAIL_SERVER=production-smtp-server
SENTRY_DSN=your-sentry-dsn
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Ensure MySQL is running
   - Check database credentials
   - Verify database exists

2. **Email Not Sending**
   - Check SMTP configuration
   - Verify email credentials
   - Check firewall settings

3. **File Upload Issues**
   - Check upload directory permissions
   - Verify file size limits
   - Ensure allowed file extensions

4. **AI Features Not Working**
   - Ensure sufficient historical data
   - Check scikit-learn installation
   - Verify model training completion

### Logs
Application logs are available in the console output. For production, configure proper logging to files.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with appropriate tests
4. Submit a pull request

## License

This project is proprietary software developed for internal use.

## Support

For technical support or questions:
- Check the troubleshooting section
- Review the API documentation
- Contact the development team

## Version History

- **v1.0.0** - Initial release with core functionality
- **v1.1.0** - Added AI features and advanced reporting
- **v1.2.0** - Enhanced UI and mobile responsiveness
- **v1.3.0** - Performance improvements and bug fixes

---

**METS System** - Streamlining Material Management for Industrial Operations
