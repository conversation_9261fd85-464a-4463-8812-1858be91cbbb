#!/usr/bin/env python3
"""
Standalone table creation script for METS system
Creates tables without importing the full app
"""

import pymysql
import sys
from datetime import datetime

def create_database():
    """Create the METS database"""
    print("🗄️  Creating METS database...")
    
    try:
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # Create database
        cursor.execute("CREATE DATABASE IF NOT EXISTS mets_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("✅ Database 'mets_system' created successfully")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Database creation failed: {e}")
        return False

def create_tables():
    """Create tables using raw SQL"""
    print("\n📋 Creating database tables...")
    
    try:
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            database='mets_system',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # Users table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(80) NOT NULL UNIQUE,
            email VARCHAR(120) NOT NULL UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            employee_id VARCHAR(20) UNIQUE,
            role VARCHAR(50) NOT NULL DEFAULT 'MATERIAL_SPECIALIST',
            department VARCHAR(100),
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            is_admin BOOLEAN NOT NULL DEFAULT FALSE,
            phone VARCHAR(20),
            extension VARCHAR(10),
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_login DATETIME,
            INDEX idx_username (username),
            INDEX idx_email (email),
            INDEX idx_role (role),
            INDEX idx_active (is_active)
        )
        """)
        
        # Material Requests table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS material_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            mr_number VARCHAR(50) NOT NULL UNIQUE,
            title VARCHAR(200) NOT NULL,
            description TEXT,
            priority VARCHAR(20) NOT NULL DEFAULT 'NORMAL',
            category VARCHAR(50),
            project_code VARCHAR(50),
            cost_center VARCHAR(50),
            status VARCHAR(50) NOT NULL DEFAULT 'DRAFT',
            current_stage VARCHAR(50) NOT NULL DEFAULT 'MR_CREATION',
            workflow_step INT NOT NULL DEFAULT 1,
            created_by_id INT NOT NULL,
            assigned_to_id INT,
            requested_date DATE,
            required_date DATE,
            approved_date DATETIME,
            completed_date DATETIME,
            estimated_cost DECIMAL(15,2),
            approved_budget DECIMAL(15,2),
            actual_cost DECIMAL(15,2),
            currency VARCHAR(3) NOT NULL DEFAULT 'USD',
            justification TEXT,
            technical_specifications TEXT,
            delivery_location VARCHAR(200),
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by_id) REFERENCES users(id),
            FOREIGN KEY (assigned_to_id) REFERENCES users(id),
            INDEX idx_mr_number (mr_number),
            INDEX idx_status (status),
            INDEX idx_stage (current_stage),
            INDEX idx_created_by (created_by_id),
            INDEX idx_assigned_to (assigned_to_id),
            INDEX idx_created_at (created_at)
        )
        """)
        
        # Material Request Items table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS material_request_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            material_request_id INT NOT NULL,
            item_code VARCHAR(50),
            description TEXT NOT NULL,
            specification TEXT,
            quantity DECIMAL(10,2) NOT NULL,
            unit VARCHAR(20) NOT NULL,
            unit_price DECIMAL(15,2),
            total_price DECIMAL(15,2),
            manufacturer VARCHAR(100),
            model_number VARCHAR(100),
            remarks TEXT,
            status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (material_request_id) REFERENCES material_requests(id) ON DELETE CASCADE,
            INDEX idx_mr_id (material_request_id)
        )
        """)
        
        # Workflow Actions table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS workflow_actions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            material_request_id INT NOT NULL,
            user_id INT NOT NULL,
            stage VARCHAR(50) NOT NULL,
            action VARCHAR(20) NOT NULL,
            comments TEXT,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (material_request_id) REFERENCES material_requests(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id),
            INDEX idx_mr_id (material_request_id),
            INDEX idx_user_id (user_id),
            INDEX idx_stage (stage),
            INDEX idx_created_at (created_at)
        )
        """)
        
        # User Activities table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS user_activities (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            action VARCHAR(100) NOT NULL,
            description TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            entity_type VARCHAR(50),
            entity_id INT,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_id (user_id),
            INDEX idx_action (action),
            INDEX idx_entity (entity_type, entity_id),
            INDEX idx_created_at (created_at)
        )
        """)
        
        connection.commit()
        print("✅ Core tables created successfully")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Table creation failed: {e}")
        return False

def create_admin_user():
    """Create admin user"""
    print("\n👤 Creating admin user...")
    
    try:
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            database='mets_system',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # Check if admin exists
        cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
        admin_count = cursor.fetchone()[0]
        
        if admin_count > 0:
            print("✅ Admin user already exists")
            cursor.close()
            connection.close()
            return True
        
        # Create password hash (simplified for testing)
        from werkzeug.security import generate_password_hash
        password_hash = generate_password_hash('admin123')
        
        # Insert admin user
        cursor.execute("""
        INSERT INTO users (username, email, password_hash, first_name, last_name, role, department, is_admin, employee_id)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, ('admin', '<EMAIL>', password_hash, 'System', 'Administrator', 'ADMIN', 'IT', True, 'EMP001'))
        
        connection.commit()
        print("✅ Admin user created successfully")
        print("   Username: admin")
        print("   Password: admin123")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Admin user creation failed: {e}")
        return False

def verify_setup():
    """Verify the database setup"""
    print("\n🔍 Verifying database setup...")
    
    try:
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            database='mets_system',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # Check tables
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        table_count = len(tables)
        
        print(f"✅ Found {table_count} tables in database")
        
        # Check if admin user exists
        cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
        admin_count = cursor.fetchone()[0]
        
        if admin_count > 0:
            print("✅ Admin user exists")
        else:
            print("⚠️  Admin user not found")
        
        cursor.close()
        connection.close()
        
        return table_count > 0
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main setup function"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    METS Database Setup                       ║
    ║              Standalone Table Creation                      ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    steps_completed = 0
    total_steps = 4
    
    # Step 1: Create database
    if create_database():
        steps_completed += 1
    else:
        print("❌ Cannot proceed without database")
        return False
    
    # Step 2: Create tables
    if create_tables():
        steps_completed += 1
    else:
        print("❌ Cannot proceed without tables")
        return False
    
    # Step 3: Create admin user
    if create_admin_user():
        steps_completed += 1
    
    # Step 4: Verify setup
    if verify_setup():
        steps_completed += 1
    
    print(f"\n📊 Setup Results: {steps_completed}/{total_steps} steps completed")
    
    if steps_completed >= 3:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🎉 DATABASE READY! 🎉                    ║
    ║                                                              ║
    ║  The METS database has been set up successfully.            ║
    ║  Core tables and admin user are ready.                      ║
    ║                                                              ║
    ║  Default login credentials:                                  ║
    ║  Username: admin                                             ║
    ║  Password: admin123                                          ║
    ║                                                              ║
    ║  Next step: Test the application!                           ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return True
    else:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    ⚠️  SETUP INCOMPLETE ⚠️                  ║
    ║                                                              ║
    ║  Database setup encountered issues.                         ║
    ║  Check the error messages above.                             ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
