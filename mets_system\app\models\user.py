from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
from app import db

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    employee_id = db.Column(db.String(20), unique=True, nullable=True)
    
    # Role and permissions
    role = db.Column(db.String(50), nullable=False, default='MATERIAL_SPECIALIST')
    department = db.Column(db.String(100), nullable=True)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    is_admin = db.Column(db.Boolean, default=False, nullable=False)
    
    # Contact information
    phone = db.Column(db.String(20), nullable=True)
    extension = db.Column(db.String(10), nullable=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime, nullable=True)
    
    # Relationships
    created_mrs = db.relationship('MaterialRequest', foreign_keys='MaterialRequest.created_by_id', backref='creator', lazy='dynamic')
    assigned_mrs = db.relationship('MaterialRequest', foreign_keys='MaterialRequest.assigned_to_id', backref='assignee', lazy='dynamic')
    workflow_actions = db.relationship('WorkflowAction', backref='user', lazy='dynamic')
    user_groups = db.relationship('UserGroup', backref='user', lazy='dynamic')
    
    def __init__(self, **kwargs):
        super(User, self).__init__(**kwargs)
    
    def set_password(self, password):
        """Set password hash"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check password against hash"""
        return check_password_hash(self.password_hash, password)
    
    @property
    def full_name(self):
        """Get full name"""
        return f"{self.first_name} {self.last_name}"
    
    def has_role(self, role):
        """Check if user has specific role"""
        return self.role == role
    
    def can_approve_stage(self, stage):
        """Check if user can approve specific workflow stage"""
        role_permissions = {
            'MR_CREATION': ['MATERIAL_SPECIALIST', 'TECHNICAL_BUYER', 'MATERIAL_COORDINATOR'],
            'MR_APPROVAL': ['SUPERVISOR', 'SECTION_HEAD_EXPAT', 'SECTION_HEAD_LOCAL', 'WAREHOUSE_KEEPER', 'TECH_COMMITTEE', 'MAINTENANCE_MANAGER'],
            'RECEIVING_OFFERS': ['MATERIAL_COORDINATOR', 'TECHNICAL_BUYER'],
            'TECHNICAL_EVAL': ['TECHNICAL_BUYER', 'SUPERVISOR', 'SECTION_HEAD_EXPAT', 'SECTION_HEAD_LOCAL'],
            'COMMERCIAL_EVAL': ['COMMERCIAL_COMMITTEE', 'SECTION_HEAD_EXPAT', 'SECTION_HEAD_LOCAL'],
            'MANAGEMENT_APPR': ['MAINTENANCE_MANAGER', 'SECTION_HEAD_EXPAT'],
            'PURCHASING_PROCESS': ['MATERIAL_COORDINATOR', 'TECHNICAL_BUYER'],
            'CLOSING_OUT': ['MATERIAL_SPECIALIST', 'WAREHOUSE_KEEPER']
        }
        
        return self.role in role_permissions.get(stage, []) or self.is_admin
    
    def get_pending_mrs_count(self):
        """Get count of MRs pending user action"""
        from app.models.material_request import MaterialRequest
        return MaterialRequest.query.filter_by(assigned_to_id=self.id, status='PENDING').count()
    
    def to_dict(self):
        """Convert user to dictionary"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'role': self.role,
            'department': self.department,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
    
    def __repr__(self):
        return f'<User {self.username}>'


class Group(db.Model):
    __tablename__ = 'groups'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    user_groups = db.relationship('UserGroup', backref='group', lazy='dynamic')
    
    def __repr__(self):
        return f'<Group {self.name}>'


class UserGroup(db.Model):
    __tablename__ = 'user_groups'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    group_id = db.Column(db.Integer, db.ForeignKey('groups.id'), nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    __table_args__ = (db.UniqueConstraint('user_id', 'group_id', name='unique_user_group'),)
    
    def __repr__(self):
        return f'<UserGroup user_id={self.user_id} group_id={self.group_id}>'


class UserActivity(db.Model):
    __tablename__ = 'user_activities'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    action = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.Text, nullable=True)
    
    # Related entity information
    entity_type = db.Column(db.String(50), nullable=True)  # e.g., 'MaterialRequest', 'User'
    entity_id = db.Column(db.Integer, nullable=True)
    
    # Timestamp
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    user = db.relationship('User', backref='activities')
    
    def __repr__(self):
        return f'<UserActivity {self.user_id}: {self.action}>'
