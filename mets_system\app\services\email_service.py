from flask import current_app, render_template
from flask_mail import Message
from app import mail
import threading

def send_async_email(app, msg):
    """Send email asynchronously"""
    with app.app_context():
        try:
            mail.send(msg)
        except Exception as e:
            print(f"Error sending email: {str(e)}")

def send_email(to, subject, template, **kwargs):
    """Send email with template"""
    try:
        msg = Message(
            subject=subject,
            recipients=[to] if isinstance(to, str) else to,
            html=template,
            sender=current_app.config['MAIL_DEFAULT_SENDER']
        )
        
        # Send asynchronously
        thread = threading.Thread(
            target=send_async_email,
            args=(current_app._get_current_object(), msg)
        )
        thread.start()
        
        return True
    except Exception as e:
        print(f"Error preparing email: {str(e)}")
        return False

def send_notification_email(to_user, subject, template, **kwargs):
    """Send notification email to user"""
    if not to_user or not to_user.email:
        return False
    
    try:
        # Generate email content based on template
        email_content = generate_email_content(template, to_user=to_user, **kwargs)
        
        return send_email(
            to=to_user.email,
            subject=subject,
            template=email_content
        )
    except Exception as e:
        print(f"Error sending notification email: {str(e)}")
        return False

def generate_email_content(template_name, **kwargs):
    """Generate email content from template"""
    templates = {
        'mr_approval_request': generate_approval_request_email,
        'mr_approved': generate_approved_email,
        'mr_rejected': generate_rejected_email,
        'mr_returned': generate_returned_email,
        'mr_completed': generate_completed_email,
        'overdue_reminder': generate_overdue_reminder_email
    }
    
    generator = templates.get(template_name, generate_default_email)
    return generator(**kwargs)

def generate_approval_request_email(to_user, mr, **kwargs):
    """Generate approval request email"""
    return f"""
    <html>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #2c5aa0;">Material Request Approval Required</h2>
            
            <p>Dear {to_user.first_name},</p>
            
            <p>A new Material Request requires your approval:</p>
            
            <div style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #2c5aa0; margin: 20px 0;">
                <h3 style="margin-top: 0;">MR Details</h3>
                <p><strong>MR Number:</strong> {mr.mr_number}</p>
                <p><strong>Title:</strong> {mr.title}</p>
                <p><strong>Priority:</strong> {mr.priority}</p>
                <p><strong>Current Stage:</strong> {mr.current_stage.replace('_', ' ').title()}</p>
                <p><strong>Created By:</strong> {mr.creator.full_name if mr.creator else 'N/A'}</p>
                <p><strong>Created Date:</strong> {mr.created_at.strftime('%Y-%m-%d %H:%M') if mr.created_at else 'N/A'}</p>
            </div>
            
            <p>Please log in to the METS system to review and approve this request.</p>
            
            <div style="margin: 30px 0;">
                <a href="#" style="background-color: #2c5aa0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                    Review Material Request
                </a>
            </div>
            
            <p style="color: #666; font-size: 12px; margin-top: 30px;">
                This is an automated message from the METS (Material Management System). Please do not reply to this email.
            </p>
        </div>
    </body>
    </html>
    """

def generate_approved_email(to_user, mr, comments=None, **kwargs):
    """Generate approval notification email"""
    return f"""
    <html>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #28a745;">Material Request Approved</h2>
            
            <p>Dear {to_user.first_name},</p>
            
            <p>Your Material Request has been approved and moved to the next stage:</p>
            
            <div style="background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;">
                <h3 style="margin-top: 0;">MR Details</h3>
                <p><strong>MR Number:</strong> {mr.mr_number}</p>
                <p><strong>Title:</strong> {mr.title}</p>
                <p><strong>Current Stage:</strong> {mr.current_stage.replace('_', ' ').title()}</p>
                <p><strong>Status:</strong> {mr.status}</p>
            </div>
            
            {f'<div style="background-color: #f8f9fa; padding: 15px; margin: 20px 0;"><h4>Comments:</h4><p>{comments}</p></div>' if comments else ''}
            
            <p>You can track the progress of your Material Request in the METS system.</p>
            
            <div style="margin: 30px 0;">
                <a href="#" style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                    View Material Request
                </a>
            </div>
            
            <p style="color: #666; font-size: 12px; margin-top: 30px;">
                This is an automated message from the METS (Material Management System). Please do not reply to this email.
            </p>
        </div>
    </body>
    </html>
    """

def generate_rejected_email(to_user, mr, comments=None, **kwargs):
    """Generate rejection notification email"""
    return f"""
    <html>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #dc3545;">Material Request Rejected</h2>
            
            <p>Dear {to_user.first_name},</p>
            
            <p>Unfortunately, your Material Request has been rejected:</p>
            
            <div style="background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 20px 0;">
                <h3 style="margin-top: 0;">MR Details</h3>
                <p><strong>MR Number:</strong> {mr.mr_number}</p>
                <p><strong>Title:</strong> {mr.title}</p>
                <p><strong>Status:</strong> {mr.status}</p>
            </div>
            
            {f'<div style="background-color: #f8f9fa; padding: 15px; margin: 20px 0;"><h4>Rejection Reason:</h4><p>{comments}</p></div>' if comments else ''}
            
            <p>Please review the rejection reason and contact the approver if you need clarification.</p>
            
            <div style="margin: 30px 0;">
                <a href="#" style="background-color: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                    View Material Request
                </a>
            </div>
            
            <p style="color: #666; font-size: 12px; margin-top: 30px;">
                This is an automated message from the METS (Material Management System). Please do not reply to this email.
            </p>
        </div>
    </body>
    </html>
    """

def generate_returned_email(to_user, mr, comments=None, **kwargs):
    """Generate return notification email"""
    return f"""
    <html>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #ffc107;">Material Request Returned</h2>
            
            <p>Dear {to_user.first_name},</p>
            
            <p>Your Material Request has been returned for revision:</p>
            
            <div style="background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 20px 0;">
                <h3 style="margin-top: 0;">MR Details</h3>
                <p><strong>MR Number:</strong> {mr.mr_number}</p>
                <p><strong>Title:</strong> {mr.title}</p>
                <p><strong>Status:</strong> {mr.status}</p>
            </div>
            
            {f'<div style="background-color: #f8f9fa; padding: 15px; margin: 20px 0;"><h4>Return Reason:</h4><p>{comments}</p></div>' if comments else ''}
            
            <p>Please review the comments and make the necessary revisions before resubmitting.</p>
            
            <div style="margin: 30px 0;">
                <a href="#" style="background-color: #ffc107; color: #212529; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                    Edit Material Request
                </a>
            </div>
            
            <p style="color: #666; font-size: 12px; margin-top: 30px;">
                This is an automated message from the METS (Material Management System). Please do not reply to this email.
            </p>
        </div>
    </body>
    </html>
    """

def generate_completed_email(to_user, mr, **kwargs):
    """Generate completion notification email"""
    return f"""
    <html>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #28a745;">Material Request Completed</h2>
            
            <p>Dear {to_user.first_name},</p>
            
            <p>Your Material Request has been completed successfully:</p>
            
            <div style="background-color: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;">
                <h3 style="margin-top: 0;">MR Details</h3>
                <p><strong>MR Number:</strong> {mr.mr_number}</p>
                <p><strong>Title:</strong> {mr.title}</p>
                <p><strong>Status:</strong> {mr.status}</p>
                <p><strong>Completed Date:</strong> {mr.completed_date.strftime('%Y-%m-%d %H:%M') if mr.completed_date else 'N/A'}</p>
            </div>
            
            <p>Thank you for using the METS system. Your materials should be delivered according to the agreed timeline.</p>
            
            <div style="margin: 30px 0;">
                <a href="#" style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                    View Material Request
                </a>
            </div>
            
            <p style="color: #666; font-size: 12px; margin-top: 30px;">
                This is an automated message from the METS (Material Management System). Please do not reply to this email.
            </p>
        </div>
    </body>
    </html>
    """

def generate_overdue_reminder_email(to_user, mr, days_overdue, **kwargs):
    """Generate overdue reminder email"""
    return f"""
    <html>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #dc3545;">Overdue Material Request Reminder</h2>
            
            <p>Dear {to_user.first_name},</p>
            
            <p>This is a reminder that you have a Material Request that is overdue for action:</p>
            
            <div style="background-color: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 20px 0;">
                <h3 style="margin-top: 0;">MR Details</h3>
                <p><strong>MR Number:</strong> {mr.mr_number}</p>
                <p><strong>Title:</strong> {mr.title}</p>
                <p><strong>Current Stage:</strong> {mr.current_stage.replace('_', ' ').title()}</p>
                <p><strong>Days Overdue:</strong> {days_overdue}</p>
            </div>
            
            <p>Please take action on this Material Request as soon as possible to avoid further delays.</p>
            
            <div style="margin: 30px 0;">
                <a href="#" style="background-color: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                    Review Material Request
                </a>
            </div>
            
            <p style="color: #666; font-size: 12px; margin-top: 30px;">
                This is an automated message from the METS (Material Management System). Please do not reply to this email.
            </p>
        </div>
    </body>
    </html>
    """

def generate_default_email(**kwargs):
    """Generate default email template"""
    return """
    <html>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2>METS System Notification</h2>
            <p>You have received a notification from the Material Management System.</p>
            <p>Please log in to the system for more details.</p>
        </div>
    </body>
    </html>
    """

def send_bulk_reminders():
    """Send bulk reminder emails for overdue MRs"""
    from app.models.material_request import MaterialRequest
    from app.models.user import User
    from datetime import datetime, timedelta
    
    # Find overdue MRs (simplified logic)
    overdue_threshold = datetime.now() - timedelta(days=3)
    
    overdue_mrs = MaterialRequest.query.filter(
        MaterialRequest.status == 'PENDING',
        MaterialRequest.updated_at < overdue_threshold
    ).all()
    
    for mr in overdue_mrs:
        if mr.assigned_to_id:
            assignee = User.query.get(mr.assigned_to_id)
            if assignee:
                days_overdue = (datetime.now() - mr.updated_at).days
                send_notification_email(
                    to_user=assignee,
                    subject=f'Overdue Material Request: {mr.mr_number}',
                    template='overdue_reminder',
                    mr=mr,
                    days_overdue=days_overdue
                )
