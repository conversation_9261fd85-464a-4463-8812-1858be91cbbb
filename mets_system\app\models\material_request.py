from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import Numeric
from datetime import datetime
from app import db

class MaterialRequest(db.Model):
    __tablename__ = 'material_requests'

    id = db.Column(db.Integer, primary_key=True)
    mr_number = db.Column(db.String(50), unique=True, nullable=False, index=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)

    # Request details
    priority = db.Column(db.String(20), default='NORMAL', nullable=False)  # LOW, NORMAL, HIGH, URGENT
    category = db.Column(db.String(50), nullable=True)
    project_code = db.Column(db.String(50), nullable=True)
    cost_center = db.Column(db.String(50), nullable=True)

    # Status and workflow
    status = db.Column(db.String(50), default='DRAFT', nullable=False)
    current_stage = db.Column(db.String(50), default='MR_CREATION', nullable=False)
    workflow_step = db.Column(db.Integer, default=1, nullable=False)

    # User assignments
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    assigned_to_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)

    # Dates
    requested_date = db.Column(db.Date, nullable=True)
    required_date = db.Column(db.Date, nullable=True)
    approved_date = db.Column(db.DateTime, nullable=True)
    completed_date = db.Column(db.DateTime, nullable=True)

    # Financial information
    estimated_cost = db.Column(Numeric(15, 2), nullable=True)
    approved_budget = db.Column(Numeric(15, 2), nullable=True)
    actual_cost = db.Column(Numeric(15, 2), nullable=True)
    currency = db.Column(db.String(3), default='USD', nullable=False)

    # Additional fields
    justification = db.Column(db.Text, nullable=True)
    technical_specifications = db.Column(db.Text, nullable=True)
    delivery_location = db.Column(db.String(200), nullable=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    items = db.relationship('MaterialRequestItem', backref='material_request', lazy='dynamic', cascade='all, delete-orphan')
    attachments = db.relationship('Attachment', backref='material_request', lazy='dynamic', cascade='all, delete-orphan')
    workflow_actions = db.relationship('WorkflowAction', backref='material_request', lazy='dynamic', cascade='all, delete-orphan')
    offers = db.relationship('Offer', backref='material_request', lazy='dynamic', cascade='all, delete-orphan')
    evaluations = db.relationship('Evaluation', backref='material_request', lazy='dynamic', cascade='all, delete-orphan')

    def __init__(self, **kwargs):
        super(MaterialRequest, self).__init__(**kwargs)
        if not self.mr_number:
            self.mr_number = self.generate_mr_number()

    @staticmethod
    def generate_mr_number():
        """Generate unique MR number"""
        from datetime import datetime
        year = datetime.now().year

        # Get the last MR number for this year
        last_mr = MaterialRequest.query.filter(
            MaterialRequest.mr_number.like(f'MR-{year}-%')
        ).order_by(MaterialRequest.id.desc()).first()

        if last_mr:
            # Extract sequence number and increment
            try:
                last_seq = int(last_mr.mr_number.split('-')[-1])
                new_seq = last_seq + 1
            except (ValueError, IndexError):
                new_seq = 1
        else:
            new_seq = 1

        return f'MR-{year}-{new_seq:04d}'

    def get_current_approver(self):
        """Get current approver based on workflow stage"""
        stage_approvers = {
            'MR_CREATION': 'MATERIAL_SPECIALIST',
            'MR_APPROVAL': 'SECTION_HEAD_EXPAT',
            'RECEIVING_OFFERS': 'MATERIAL_COORDINATOR',
            'TECHNICAL_EVAL': 'TECHNICAL_BUYER',
            'COMMERCIAL_EVAL': 'COMMERCIAL_COMMITTEE',
            'MANAGEMENT_APPR': 'MAINTENANCE_MANAGER',
            'PURCHASING_PROCESS': 'MATERIAL_COORDINATOR',
            'CLOSING_OUT': 'WAREHOUSE_KEEPER'
        }

        required_role = stage_approvers.get(self.current_stage)
        if required_role:
            from app.models.user import User
            return User.query.filter_by(role=required_role, is_active=True).first()
        return None

    def can_edit(self, user):
        """Check if user can edit this MR"""
        if user.is_admin:
            return True

        # Creator can edit in draft or initial stages
        if self.created_by_id == user.id and self.current_stage in ['MR_CREATION', 'MR_APPROVAL']:
            return True

        # Assigned user can edit
        if self.assigned_to_id == user.id:
            return True

        return False

    def can_approve(self, user):
        """Check if user can approve this MR at current stage"""
        return user.can_approve_stage(self.current_stage)

    def get_next_stage(self):
        """Get next workflow stage"""
        from config import Config
        stages = Config.WORKFLOW_STAGES

        try:
            current_index = stages.index(self.current_stage)
            if current_index < len(stages) - 1:
                return stages[current_index + 1]
        except ValueError:
            pass

        return None

    def advance_workflow(self, user, action='APPROVE', comments=None):
        """Advance MR to next workflow stage"""
        from app.models.workflow import WorkflowAction

        # Record the action
        workflow_action = WorkflowAction(
            material_request_id=self.id,
            user_id=user.id,
            stage=self.current_stage,
            action=action,
            comments=comments,
            created_at=datetime.utcnow()
        )
        db.session.add(workflow_action)

        if action == 'APPROVE':
            next_stage = self.get_next_stage()
            if next_stage:
                self.current_stage = next_stage
                self.workflow_step += 1

                # Assign to next approver
                next_approver = self.get_current_approver()
                if next_approver:
                    self.assigned_to_id = next_approver.id

                # Update status
                if next_stage == 'CLOSING_OUT':
                    self.status = 'COMPLETED'
                    self.completed_date = datetime.utcnow()
                else:
                    self.status = 'PENDING'
            else:
                self.status = 'COMPLETED'
                self.completed_date = datetime.utcnow()

        elif action == 'REJECT':
            self.status = 'REJECTED'

        elif action == 'RETURN':
            self.status = 'RETURNED'
            # Assign back to creator
            self.assigned_to_id = self.created_by_id

        self.updated_at = datetime.utcnow()
        db.session.commit()

    def to_dict(self):
        """Convert MR to dictionary"""
        return {
            'id': self.id,
            'mr_number': self.mr_number,
            'title': self.title,
            'description': self.description,
            'priority': self.priority,
            'status': self.status,
            'current_stage': self.current_stage,
            'workflow_step': self.workflow_step,
            'created_by': self.creator.full_name if self.creator else None,
            'assigned_to': self.assignee.full_name if self.assignee else None,
            'estimated_cost': float(self.estimated_cost) if self.estimated_cost else None,
            'currency': self.currency,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'required_date': self.required_date.isoformat() if self.required_date else None
        }

    def __repr__(self):
        return f'<MaterialRequest {self.mr_number}>'


class MaterialRequestItem(db.Model):
    __tablename__ = 'material_request_items'

    id = db.Column(db.Integer, primary_key=True)
    material_request_id = db.Column(db.Integer, db.ForeignKey('material_requests.id'), nullable=False)

    # Item details
    item_code = db.Column(db.String(50), nullable=True)
    description = db.Column(db.Text, nullable=False)
    specification = db.Column(db.Text, nullable=True)
    quantity = db.Column(db.Decimal(10, 2), nullable=False)
    unit = db.Column(db.String(20), nullable=False)

    # Pricing
    unit_price = db.Column(db.Decimal(15, 2), nullable=True)
    total_price = db.Column(db.Decimal(15, 2), nullable=True)

    # Additional information
    manufacturer = db.Column(db.String(100), nullable=True)
    model_number = db.Column(db.String(100), nullable=True)
    remarks = db.Column(db.Text, nullable=True)

    # Status
    status = db.Column(db.String(50), default='PENDING', nullable=False)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    def to_dict(self):
        """Convert item to dictionary"""
        return {
            'id': self.id,
            'item_code': self.item_code,
            'description': self.description,
            'specification': self.specification,
            'quantity': float(self.quantity),
            'unit': self.unit,
            'unit_price': float(self.unit_price) if self.unit_price else None,
            'total_price': float(self.total_price) if self.total_price else None,
            'manufacturer': self.manufacturer,
            'model_number': self.model_number,
            'status': self.status
        }

    def __repr__(self):
        return f'<MaterialRequestItem {self.description[:50]}>'
