"""
AI Service for METS System
Provides AI-powered features for workflow optimization and predictions
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
import pickle
import os
from app import db
from app.models.user import User
from app.models.material_request import MaterialRequest
from app.models.workflow import WorkflowAction

class AIWorkflowAssistant:
    """AI assistant for workflow management"""
    
    def __init__(self):
        self.model_path = 'ai_models'
        self.approval_model = None
        self.approver_model = None
        self.label_encoders = {}
        
        # Create model directory if it doesn't exist
        if not os.path.exists(self.model_path):
            os.makedirs(self.model_path)
    
    def train_approval_prediction_model(self):
        """Train model to predict approval outcomes"""
        try:
            # Get historical data
            data = self._get_historical_data()
            
            if len(data) < 50:  # Need minimum data for training
                print("Insufficient data for training approval prediction model")
                return False
            
            # Prepare features
            features = ['priority_encoded', 'category_encoded', 'estimated_cost', 
                       'creator_role_encoded', 'stage_encoded', 'items_count']
            
            X = data[features]
            y = data['approved']  # 1 for approved, 0 for rejected/returned
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # Train model
            self.approval_model = RandomForestClassifier(
                n_estimators=100, random_state=42
            )
            self.approval_model.fit(X_train, y_train)
            
            # Evaluate model
            y_pred = self.approval_model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            print(f"Approval prediction model accuracy: {accuracy:.2f}")
            
            # Save model
            model_file = os.path.join(self.model_path, 'approval_model.pkl')
            with open(model_file, 'wb') as f:
                pickle.dump({
                    'model': self.approval_model,
                    'label_encoders': self.label_encoders,
                    'accuracy': accuracy
                }, f)
            
            return True
            
        except Exception as e:
            print(f"Error training approval prediction model: {str(e)}")
            return False
    
    def train_approver_suggestion_model(self):
        """Train model to suggest next approvers"""
        try:
            # Get historical workflow data
            data = self._get_workflow_data()
            
            if len(data) < 30:
                print("Insufficient data for training approver suggestion model")
                return False
            
            # Prepare features for approver prediction
            features = ['stage_encoded', 'priority_encoded', 'category_encoded', 
                       'estimated_cost', 'creator_role_encoded']
            
            X = data[features]
            y = data['approver_role_encoded']
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # Train model
            self.approver_model = RandomForestClassifier(
                n_estimators=100, random_state=42
            )
            self.approver_model.fit(X_train, y_train)
            
            # Evaluate model
            y_pred = self.approver_model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            print(f"Approver suggestion model accuracy: {accuracy:.2f}")
            
            # Save model
            model_file = os.path.join(self.model_path, 'approver_model.pkl')
            with open(model_file, 'wb') as f:
                pickle.dump({
                    'model': self.approver_model,
                    'label_encoders': self.label_encoders,
                    'accuracy': accuracy
                }, f)
            
            return True
            
        except Exception as e:
            print(f"Error training approver suggestion model: {str(e)}")
            return False
    
    def load_models(self):
        """Load trained models"""
        try:
            # Load approval prediction model
            approval_file = os.path.join(self.model_path, 'approval_model.pkl')
            if os.path.exists(approval_file):
                with open(approval_file, 'rb') as f:
                    approval_data = pickle.load(f)
                    self.approval_model = approval_data['model']
                    self.label_encoders.update(approval_data['label_encoders'])
            
            # Load approver suggestion model
            approver_file = os.path.join(self.model_path, 'approver_model.pkl')
            if os.path.exists(approver_file):
                with open(approver_file, 'rb') as f:
                    approver_data = pickle.load(f)
                    self.approver_model = approver_data['model']
                    self.label_encoders.update(approver_data['label_encoders'])
            
            return True
            
        except Exception as e:
            print(f"Error loading AI models: {str(e)}")
            return False
    
    def predict_approval_outcome(self, mr):
        """Predict approval outcome for Material Request"""
        try:
            if not self.approval_model:
                self.load_models()
            
            if not self.approval_model:
                return {'prediction': 'unknown', 'confidence': 0.0, 'message': 'Model not available'}
            
            # Prepare features
            features = self._prepare_mr_features(mr)
            
            # Make prediction
            prediction_proba = self.approval_model.predict_proba([features])[0]
            prediction = self.approval_model.predict([features])[0]
            
            confidence = max(prediction_proba)
            outcome = 'approved' if prediction == 1 else 'rejected'
            
            return {
                'prediction': outcome,
                'confidence': float(confidence),
                'approval_probability': float(prediction_proba[1]) if len(prediction_proba) > 1 else 0.0,
                'rejection_probability': float(prediction_proba[0]) if len(prediction_proba) > 0 else 0.0
            }
            
        except Exception as e:
            return {'prediction': 'error', 'confidence': 0.0, 'message': str(e)}
    
    def suggest_next_approver(self, mr):
        """Suggest next approver for Material Request"""
        try:
            if not self.approver_model:
                self.load_models()
            
            if not self.approver_model:
                return self._fallback_approver_suggestion(mr)
            
            # Prepare features
            features = self._prepare_mr_features(mr)
            
            # Make prediction
            prediction_proba = self.approver_model.predict_proba([features])[0]
            predicted_role_encoded = self.approver_model.predict([features])[0]
            
            # Decode role
            role_encoder = self.label_encoders.get('approver_role')
            if role_encoder:
                predicted_role = role_encoder.inverse_transform([predicted_role_encoded])[0]
                confidence = max(prediction_proba)
                
                # Find available users with this role
                suggested_users = User.query.filter_by(
                    role=predicted_role, 
                    is_active=True
                ).all()
                
                return {
                    'suggested_role': predicted_role,
                    'confidence': float(confidence),
                    'suggested_users': [
                        {
                            'id': user.id,
                            'name': user.full_name,
                            'email': user.email,
                            'department': user.department
                        } for user in suggested_users
                    ]
                }
            
            return self._fallback_approver_suggestion(mr)
            
        except Exception as e:
            return self._fallback_approver_suggestion(mr)
    
    def analyze_workflow_bottlenecks(self):
        """Analyze workflow bottlenecks using AI"""
        try:
            # Get current pending MRs by stage
            stage_counts = db.session.query(
                MaterialRequest.current_stage,
                db.func.count(MaterialRequest.id).label('count'),
                db.func.avg(
                    db.func.datediff(db.func.now(), MaterialRequest.updated_at)
                ).label('avg_days')
            ).filter_by(status='PENDING').group_by(MaterialRequest.current_stage).all()
            
            bottlenecks = []
            for stage, count, avg_days in stage_counts:
                severity = 'low'
                if count > 10 and avg_days > 5:
                    severity = 'high'
                elif count > 5 or avg_days > 3:
                    severity = 'medium'
                
                bottlenecks.append({
                    'stage': stage,
                    'pending_count': count,
                    'avg_days_pending': float(avg_days) if avg_days else 0,
                    'severity': severity,
                    'recommendations': self._get_bottleneck_recommendations(stage, count, avg_days)
                })
            
            return sorted(bottlenecks, key=lambda x: x['pending_count'], reverse=True)
            
        except Exception as e:
            print(f"Error analyzing workflow bottlenecks: {str(e)}")
            return []
    
    def _get_historical_data(self):
        """Get historical data for training"""
        # Query historical MRs with their outcomes
        mrs = MaterialRequest.query.filter(
            MaterialRequest.status.in_(['COMPLETED', 'REJECTED', 'CANCELLED'])
        ).all()
        
        data = []
        for mr in mrs:
            # Determine if MR was ultimately approved (reached completion)
            approved = 1 if mr.status == 'COMPLETED' else 0
            
            # Get creator role
            creator_role = mr.creator.role if mr.creator else 'UNKNOWN'
            
            # Count items
            items_count = mr.items.count()
            
            data.append({
                'mr_id': mr.id,
                'priority': mr.priority,
                'category': mr.category or 'GENERAL',
                'estimated_cost': float(mr.estimated_cost) if mr.estimated_cost else 0.0,
                'creator_role': creator_role,
                'current_stage': mr.current_stage,
                'items_count': items_count,
                'approved': approved
            })
        
        df = pd.DataFrame(data)
        
        if len(df) > 0:
            # Encode categorical variables
            categorical_columns = ['priority', 'category', 'creator_role', 'current_stage']
            
            for col in categorical_columns:
                encoder = LabelEncoder()
                df[f'{col}_encoded'] = encoder.fit_transform(df[col])
                self.label_encoders[col] = encoder
        
        return df
    
    def _get_workflow_data(self):
        """Get workflow action data for training"""
        actions = WorkflowAction.query.join(MaterialRequest).join(User).all()
        
        data = []
        for action in actions:
            mr = action.material_request
            user = action.user
            
            data.append({
                'mr_id': mr.id,
                'stage': action.stage,
                'priority': mr.priority,
                'category': mr.category or 'GENERAL',
                'estimated_cost': float(mr.estimated_cost) if mr.estimated_cost else 0.0,
                'creator_role': mr.creator.role if mr.creator else 'UNKNOWN',
                'approver_role': user.role,
                'action': action.action
            })
        
        df = pd.DataFrame(data)
        
        if len(df) > 0:
            # Encode categorical variables
            categorical_columns = ['stage', 'priority', 'category', 'creator_role', 'approver_role']
            
            for col in categorical_columns:
                if col not in self.label_encoders:
                    encoder = LabelEncoder()
                    df[f'{col}_encoded'] = encoder.fit_transform(df[col])
                    self.label_encoders[col] = encoder
                else:
                    # Use existing encoder
                    encoder = self.label_encoders[col]
                    df[f'{col}_encoded'] = encoder.transform(df[col])
        
        return df
    
    def _prepare_mr_features(self, mr):
        """Prepare features for ML model"""
        # Get creator role
        creator_role = mr.creator.role if mr.creator else 'UNKNOWN'
        
        # Count items
        items_count = mr.items.count()
        
        # Encode categorical features
        priority_encoded = self._safe_encode('priority', mr.priority)
        category_encoded = self._safe_encode('category', mr.category or 'GENERAL')
        creator_role_encoded = self._safe_encode('creator_role', creator_role)
        stage_encoded = self._safe_encode('current_stage', mr.current_stage)
        
        return [
            priority_encoded,
            category_encoded,
            float(mr.estimated_cost) if mr.estimated_cost else 0.0,
            creator_role_encoded,
            stage_encoded,
            items_count
        ]
    
    def _safe_encode(self, encoder_name, value):
        """Safely encode value using label encoder"""
        try:
            encoder = self.label_encoders.get(encoder_name)
            if encoder:
                # Check if value exists in encoder classes
                if value in encoder.classes_:
                    return encoder.transform([value])[0]
                else:
                    # Return a default value (first class)
                    return 0
            return 0
        except Exception:
            return 0
    
    def _fallback_approver_suggestion(self, mr):
        """Fallback approver suggestion based on rules"""
        stage_approvers = {
            'MR_CREATION': 'MATERIAL_SPECIALIST',
            'MR_APPROVAL': 'SECTION_HEAD_EXPAT',
            'RECEIVING_OFFERS': 'MATERIAL_COORDINATOR',
            'TECHNICAL_EVAL': 'TECHNICAL_BUYER',
            'COMMERCIAL_EVAL': 'COMMERCIAL_COMMITTEE',
            'MANAGEMENT_APPR': 'MAINTENANCE_MANAGER',
            'PURCHASING_PROCESS': 'MATERIAL_COORDINATOR',
            'CLOSING_OUT': 'WAREHOUSE_KEEPER'
        }
        
        suggested_role = stage_approvers.get(mr.current_stage, 'SECTION_HEAD_EXPAT')
        
        # Find users with this role
        suggested_users = User.query.filter_by(
            role=suggested_role, 
            is_active=True
        ).all()
        
        return {
            'suggested_role': suggested_role,
            'confidence': 0.8,  # Rule-based confidence
            'suggested_users': [
                {
                    'id': user.id,
                    'name': user.full_name,
                    'email': user.email,
                    'department': user.department
                } for user in suggested_users
            ],
            'method': 'rule_based'
        }
    
    def _get_bottleneck_recommendations(self, stage, count, avg_days):
        """Get recommendations for bottlenecks"""
        recommendations = []
        
        if count > 10:
            recommendations.append("Consider adding more approvers for this stage")
            recommendations.append("Review if this stage can be parallelized")
        
        if avg_days > 5:
            recommendations.append("Set up automated reminders for this stage")
            recommendations.append("Review SLA requirements for this stage")
        
        if stage == 'RECEIVING_OFFERS':
            recommendations.append("Consider pre-approved vendor lists")
            recommendations.append("Implement automated offer collection")
        
        return recommendations

# Global AI assistant instance
ai_assistant = AIWorkflowAssistant()

def suggest_next_approver(mr):
    """Suggest next approver for Material Request"""
    return ai_assistant.suggest_next_approver(mr)

def predict_approval_outcome(mr):
    """Predict approval outcome for Material Request"""
    return ai_assistant.predict_approval_outcome(mr)

def analyze_workflow_bottlenecks():
    """Analyze workflow bottlenecks"""
    return ai_assistant.analyze_workflow_bottlenecks()

def train_ai_models():
    """Train AI models with current data"""
    print("Training AI models...")
    
    # Train approval prediction model
    approval_success = ai_assistant.train_approval_prediction_model()
    
    # Train approver suggestion model
    approver_success = ai_assistant.train_approver_suggestion_model()
    
    return {
        'approval_model': approval_success,
        'approver_model': approver_success
    }

def get_smart_insights(mr):
    """Get smart insights for Material Request"""
    insights = []
    
    try:
        # Approval prediction
        approval_prediction = predict_approval_outcome(mr)
        if approval_prediction['prediction'] != 'error':
            if approval_prediction['confidence'] > 0.7:
                insights.append({
                    'type': 'prediction',
                    'title': 'Approval Prediction',
                    'message': f"This MR has a {approval_prediction['approval_probability']:.1%} chance of approval",
                    'confidence': approval_prediction['confidence']
                })
        
        # Similar MRs
        similar_mrs = find_similar_mrs(mr)
        if similar_mrs:
            insights.append({
                'type': 'reference',
                'title': 'Similar Requests',
                'message': f"Found {len(similar_mrs)} similar requests for reference",
                'data': similar_mrs[:3]  # Top 3
            })
        
        # Processing time estimate
        estimated_time = estimate_processing_time(mr)
        if estimated_time:
            insights.append({
                'type': 'timeline',
                'title': 'Estimated Processing Time',
                'message': f"Expected completion in {estimated_time} days",
                'data': estimated_time
            })
        
    except Exception as e:
        print(f"Error generating smart insights: {str(e)}")
    
    return insights

def find_similar_mrs(mr, limit=5):
    """Find similar Material Requests"""
    try:
        # Simple similarity based on category and keywords
        similar = MaterialRequest.query.filter(
            MaterialRequest.id != mr.id,
            MaterialRequest.category == mr.category,
            MaterialRequest.status.in_(['COMPLETED', 'APPROVED'])
        ).limit(limit).all()
        
        return [{
            'id': m.id,
            'mr_number': m.mr_number,
            'title': m.title,
            'status': m.status,
            'processing_days': (m.completed_date - m.created_at).days if m.completed_date else None
        } for m in similar]
        
    except Exception as e:
        print(f"Error finding similar MRs: {str(e)}")
        return []

def estimate_processing_time(mr):
    """Estimate processing time for Material Request"""
    try:
        # Get historical data for similar MRs
        similar_completed = MaterialRequest.query.filter(
            MaterialRequest.category == mr.category,
            MaterialRequest.priority == mr.priority,
            MaterialRequest.status == 'COMPLETED',
            MaterialRequest.completed_date.isnot(None)
        ).all()
        
        if similar_completed:
            processing_times = []
            for completed_mr in similar_completed:
                days = (completed_mr.completed_date - completed_mr.created_at).days
                processing_times.append(days)
            
            if processing_times:
                avg_time = sum(processing_times) / len(processing_times)
                return int(avg_time)
        
        # Fallback to stage-based estimation
        stage_estimates = {
            'MR_CREATION': 1,
            'MR_APPROVAL': 3,
            'RECEIVING_OFFERS': 7,
            'TECHNICAL_EVAL': 5,
            'COMMERCIAL_EVAL': 3,
            'MANAGEMENT_APPR': 2,
            'PURCHASING_PROCESS': 5,
            'CLOSING_OUT': 2
        }
        
        from config import Config
        remaining_stages = Config.WORKFLOW_STAGES[Config.WORKFLOW_STAGES.index(mr.current_stage):]
        
        estimated_days = sum(stage_estimates.get(stage, 2) for stage in remaining_stages)
        return estimated_days
        
    except Exception as e:
        print(f"Error estimating processing time: {str(e)}")
        return None
