#!/usr/bin/env python3
"""
Final verification test for METS system
Comprehensive test to ensure everything is working
"""

import requests
import time
import sys
from datetime import datetime

def test_server_running():
    """Test if the server is running"""
    print("🌐 Testing server connectivity...")

    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and responding")
            return True
        else:
            print(f"⚠️  Server responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Server test failed: {e}")
        return False

def test_login_page():
    """Test login page accessibility"""
    print("\n🔐 Testing login page...")

    try:
        response = requests.get('http://localhost:5000/login', timeout=5)
        if response.status_code == 200:
            if 'METS System' in response.text and 'Username' in response.text:
                print("✅ Login page loads correctly")
                return True
            else:
                print("⚠️  Login page content incomplete")
                return False
        else:
            print(f"❌ Login page returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Login page test failed: {e}")
        return False

def test_login_functionality():
    """Test login functionality"""
    print("\n👤 Testing login functionality...")

    try:
        # Create a session
        session = requests.Session()

        # Get login page to get any CSRF tokens
        login_page = session.get('http://localhost:5000/login', timeout=5)

        # Attempt login
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }

        response = session.post('http://localhost:5000/login', data=login_data, timeout=5)

        # Check if redirected to dashboard (successful login)
        if response.status_code == 200 and ('Dashboard' in response.text or 'Welcome' in response.text):
            print("✅ Login functionality working")
            return True, session
        elif response.status_code == 302:  # Redirect
            # Follow redirect
            dashboard_response = session.get('http://localhost:5000/dashboard', timeout=5)
            if dashboard_response.status_code == 200:
                print("✅ Login successful with redirect")
                return True, session
            else:
                print("⚠️  Login redirect failed")
                return False, None
        else:
            print(f"❌ Login failed - Status: {response.status_code}")
            return False, None

    except Exception as e:
        print(f"❌ Login test failed: {e}")
        return False, None

def test_dashboard_access(session):
    """Test dashboard access"""
    print("\n📊 Testing dashboard access...")

    try:
        response = session.get('http://localhost:5000/dashboard', timeout=5)
        if response.status_code == 200:
            if 'METS System' in response.text and 'Dashboard' in response.text:
                print("✅ Dashboard accessible and loading correctly")
                return True
            else:
                print("⚠️  Dashboard content incomplete")
                return False
        else:
            print(f"❌ Dashboard returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Dashboard test failed: {e}")
        return False

def test_database_connectivity():
    """Test database connectivity through the app"""
    print("\n🗄️  Testing database connectivity...")

    try:
        # Test the database test endpoint
        response = requests.get('http://localhost:5000/test-db', timeout=10)
        if response.status_code == 200:
            if ('Database' in response.text and 'Test' in response.text) or 'Found' in response.text or 'tables' in response.text.lower():
                print("✅ Database connectivity working")
                return True
            else:
                print("⚠️  Database test page incomplete")
                print(f"   Response preview: {response.text[:200]}...")
                return False
        else:
            print(f"❌ Database test returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_all_pages(session):
    """Test all available pages"""
    print("\n📄 Testing all pages...")

    pages = {
        '/': 'Home page',
        '/dashboard': 'Dashboard',
        '/profile': 'Profile page',
        '/test-db': 'Database test page'
    }

    success_count = 0
    for url, name in pages.items():
        try:
            response = session.get(f'http://localhost:5000{url}', timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: Accessible")
                success_count += 1
            else:
                print(f"⚠️  {name}: Status {response.status_code}")
        except Exception as e:
            print(f"❌ {name}: Failed - {e}")

    return success_count == len(pages)

def generate_test_report(results):
    """Generate final test report"""
    print("\n" + "="*60)
    print("📋 FINAL TEST REPORT")
    print("="*60)

    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)

    print(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")

    print("\nDetailed Results:")
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")

    if passed_tests == total_tests:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🎉 ALL TESTS PASSED! 🎉                  ║
    ║                                                              ║
    ║  The METS system is working perfectly!                      ║
    ║  All functionality has been verified.                       ║
    ║                                                              ║
    ║  System Status: ✅ READY FOR USE                            ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return True
    elif passed_tests >= total_tests * 0.8:  # 80% pass rate
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                  ⚠️  MOSTLY WORKING ⚠️                      ║
    ║                                                              ║
    ║  Most functionality is working correctly.                   ║
    ║  Some minor issues may need attention.                      ║
    ║                                                              ║
    ║  System Status: ⚠️  USABLE WITH CAUTION                     ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return True
    else:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    ❌ TESTS FAILED ❌                       ║
    ║                                                              ║
    ║  Multiple critical issues found.                            ║
    ║  System needs troubleshooting.                              ║
    ║                                                              ║
    ║  System Status: ❌ NEEDS ATTENTION                          ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return False

def main():
    """Main test function"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    METS Final Verification                   ║
    ║              Comprehensive System Test                      ║
    ╚══════════════════════════════════════════════════════════════╝
    """)

    print("Starting comprehensive system test...")
    print("Please ensure the METS test app is running on localhost:5000")
    print()

    # Wait a moment for any server startup
    time.sleep(2)

    # Run all tests
    results = {}

    # Test 1: Server running
    results['Server Connectivity'] = test_server_running()

    # Test 2: Login page
    results['Login Page'] = test_login_page()

    # Test 3: Login functionality
    login_success, session = test_login_functionality()
    results['Login Functionality'] = login_success

    if login_success and session:
        # Test 4: Dashboard access
        results['Dashboard Access'] = test_dashboard_access(session)

        # Test 5: All pages
        results['All Pages'] = test_all_pages(session)
    else:
        results['Dashboard Access'] = False
        results['All Pages'] = False

    # Test 6: Database connectivity
    results['Database Connectivity'] = test_database_connectivity()

    # Generate final report
    success = generate_test_report(results)

    return success

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n\nUnexpected error during testing: {e}")
        sys.exit(1)
