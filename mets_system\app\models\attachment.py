from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import os
from app import db

class Attachment(db.Model):
    __tablename__ = 'attachments'
    
    id = db.Column(db.Integer, primary_key=True)
    material_request_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>('material_requests.id'), nullable=False)
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # File information
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer, nullable=False)  # Size in bytes
    mime_type = db.Column(db.String(100), nullable=True)
    
    # File metadata
    description = db.Column(db.Text, nullable=True)
    category = db.Column(db.String(50), nullable=True)  # SPECIFICATION, DRAWING, QUOTE, OTHER
    
    # Status
    is_active = db.Column(db.Bo<PERSON>, default=True, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    uploaded_by = db.relationship('User', backref='uploaded_attachments')
    
    @property
    def file_extension(self):
        """Get file extension"""
        return os.path.splitext(self.original_filename)[1].lower()
    
    @property
    def is_image(self):
        """Check if file is an image"""
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
        return self.file_extension in image_extensions
    
    @property
    def is_document(self):
        """Check if file is a document"""
        doc_extensions = {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'}
        return self.file_extension in doc_extensions
    
    @property
    def file_size_formatted(self):
        """Get formatted file size"""
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    
    def get_download_url(self):
        """Get download URL for the file"""
        return f"/api/attachments/{self.id}/download"
    
    def get_preview_url(self):
        """Get preview URL for the file (if applicable)"""
        if self.is_image:
            return f"/api/attachments/{self.id}/preview"
        return None
    
    def delete_file(self):
        """Delete the physical file"""
        try:
            if os.path.exists(self.file_path):
                os.remove(self.file_path)
            return True
        except Exception as e:
            print(f"Error deleting file {self.file_path}: {str(e)}")
            return False
    
    def to_dict(self):
        """Convert attachment to dictionary"""
        return {
            'id': self.id,
            'filename': self.filename,
            'original_filename': self.original_filename,
            'file_size': self.file_size,
            'file_size_formatted': self.file_size_formatted,
            'mime_type': self.mime_type,
            'description': self.description,
            'category': self.category,
            'is_image': self.is_image,
            'is_document': self.is_document,
            'uploaded_by': self.uploaded_by.full_name if self.uploaded_by else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'download_url': self.get_download_url(),
            'preview_url': self.get_preview_url()
        }
    
    def __repr__(self):
        return f'<Attachment {self.original_filename}>'


class Template(db.Model):
    __tablename__ = 'templates'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    category = db.Column(db.String(50), nullable=True)
    
    # Template data (JSON format)
    template_data = db.Column(db.JSON, nullable=False)
    
    # Access control
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    is_public = db.Column(db.Boolean, default=False, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    
    # Usage statistics
    usage_count = db.Column(db.Integer, default=0, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    created_by = db.relationship('User', backref='created_templates')
    
    def increment_usage(self):
        """Increment usage count"""
        self.usage_count += 1
        db.session.commit()
    
    def to_dict(self):
        """Convert template to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'template_data': self.template_data,
            'is_public': self.is_public,
            'usage_count': self.usage_count,
            'created_by': self.created_by.full_name if self.created_by else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<Template {self.name}>'
