#!/usr/bin/env python3
"""
Complete database creation for METS system
Creates all tables needed for full functionality
"""

import pymysql
import sys
from datetime import datetime

def create_complete_database():
    """Create all database tables for METS system"""
    print("🗄️  Creating complete METS database...")
    
    try:
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            database='mets_system',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # Drop existing tables if they exist (for clean setup)
        tables_to_drop = [
            'user_activities', 'evaluations', 'offer_items', 'offers', 
            'workflow_steps', 'workflow_templates', 'workflow_actions',
            'attachments', 'templates', 'material_request_items', 
            'material_requests', 'user_groups', 'groups', 'users'
        ]
        
        print("🧹 Cleaning existing tables...")
        for table in tables_to_drop:
            try:
                cursor.execute(f"DROP TABLE IF EXISTS {table}")
            except:
                pass
        
        # Create all tables
        print("📋 Creating complete table structure...")
        
        # Users table
        cursor.execute("""
        CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(80) NOT NULL UNIQUE,
            email VARCHAR(120) NOT NULL UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            employee_id VARCHAR(20) UNIQUE,
            role VARCHAR(50) NOT NULL DEFAULT 'MATERIAL_SPECIALIST',
            department VARCHAR(100),
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            is_admin BOOLEAN NOT NULL DEFAULT FALSE,
            phone VARCHAR(20),
            extension VARCHAR(10),
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_login DATETIME,
            INDEX idx_username (username),
            INDEX idx_email (email),
            INDEX idx_role (role),
            INDEX idx_active (is_active)
        )
        """)
        
        # Groups table
        cursor.execute("""
        CREATE TABLE groups (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
        """)
        
        # User Groups junction table
        cursor.execute("""
        CREATE TABLE user_groups (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            group_id INT NOT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (group_id) REFERENCES groups(id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_group (user_id, group_id)
        )
        """)
        
        # Material Requests table
        cursor.execute("""
        CREATE TABLE material_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            mr_number VARCHAR(50) NOT NULL UNIQUE,
            title VARCHAR(200) NOT NULL,
            description TEXT,
            priority VARCHAR(20) NOT NULL DEFAULT 'NORMAL',
            category VARCHAR(50),
            project_code VARCHAR(50),
            cost_center VARCHAR(50),
            status VARCHAR(50) NOT NULL DEFAULT 'DRAFT',
            current_stage VARCHAR(50) NOT NULL DEFAULT 'MR_CREATION',
            workflow_step INT NOT NULL DEFAULT 1,
            created_by_id INT NOT NULL,
            assigned_to_id INT,
            requested_date DATE,
            required_date DATE,
            approved_date DATETIME,
            completed_date DATETIME,
            estimated_cost DECIMAL(15,2),
            approved_budget DECIMAL(15,2),
            actual_cost DECIMAL(15,2),
            currency VARCHAR(3) NOT NULL DEFAULT 'USD',
            justification TEXT,
            technical_specifications TEXT,
            delivery_location VARCHAR(200),
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by_id) REFERENCES users(id),
            FOREIGN KEY (assigned_to_id) REFERENCES users(id),
            INDEX idx_mr_number (mr_number),
            INDEX idx_status (status),
            INDEX idx_stage (current_stage),
            INDEX idx_created_by (created_by_id),
            INDEX idx_assigned_to (assigned_to_id),
            INDEX idx_created_at (created_at)
        )
        """)
        
        # Material Request Items table
        cursor.execute("""
        CREATE TABLE material_request_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            material_request_id INT NOT NULL,
            item_code VARCHAR(50),
            description TEXT NOT NULL,
            specification TEXT,
            quantity DECIMAL(10,2) NOT NULL,
            unit VARCHAR(20) NOT NULL,
            unit_price DECIMAL(15,2),
            total_price DECIMAL(15,2),
            manufacturer VARCHAR(100),
            model_number VARCHAR(100),
            remarks TEXT,
            status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (material_request_id) REFERENCES material_requests(id) ON DELETE CASCADE,
            INDEX idx_mr_id (material_request_id)
        )
        """)
        
        # Workflow Templates table
        cursor.execute("""
        CREATE TABLE workflow_templates (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            is_default BOOLEAN NOT NULL DEFAULT FALSE,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
        """)
        
        # Workflow Steps table
        cursor.execute("""
        CREATE TABLE workflow_steps (
            id INT AUTO_INCREMENT PRIMARY KEY,
            template_id INT NOT NULL,
            step_number INT NOT NULL,
            stage_name VARCHAR(50) NOT NULL,
            display_name VARCHAR(100) NOT NULL,
            description TEXT,
            required_role VARCHAR(50),
            required_group_id INT,
            is_parallel BOOLEAN NOT NULL DEFAULT FALSE,
            is_optional BOOLEAN NOT NULL DEFAULT FALSE,
            sla_hours INT,
            conditions JSON,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (template_id) REFERENCES workflow_templates(id) ON DELETE CASCADE,
            FOREIGN KEY (required_group_id) REFERENCES groups(id),
            INDEX idx_template_id (template_id),
            INDEX idx_step_number (step_number)
        )
        """)
        
        # Workflow Actions table
        cursor.execute("""
        CREATE TABLE workflow_actions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            material_request_id INT NOT NULL,
            user_id INT NOT NULL,
            stage VARCHAR(50) NOT NULL,
            action VARCHAR(20) NOT NULL,
            comments TEXT,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (material_request_id) REFERENCES material_requests(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id),
            INDEX idx_mr_id (material_request_id),
            INDEX idx_user_id (user_id),
            INDEX idx_stage (stage),
            INDEX idx_created_at (created_at)
        )
        """)
        
        # Offers table
        cursor.execute("""
        CREATE TABLE offers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            material_request_id INT NOT NULL,
            supplier_name VARCHAR(200) NOT NULL,
            supplier_contact VARCHAR(100),
            supplier_email VARCHAR(120),
            supplier_phone VARCHAR(20),
            offer_number VARCHAR(50),
            offer_date DATE,
            validity_date DATE,
            total_amount DECIMAL(15,2),
            currency VARCHAR(3) NOT NULL DEFAULT 'USD',
            payment_terms VARCHAR(200),
            delivery_terms VARCHAR(200),
            delivery_time VARCHAR(100),
            status VARCHAR(50) NOT NULL DEFAULT 'RECEIVED',
            technical_score DECIMAL(5,2),
            commercial_score DECIMAL(5,2),
            overall_score DECIMAL(5,2),
            remarks TEXT,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (material_request_id) REFERENCES material_requests(id) ON DELETE CASCADE,
            INDEX idx_mr_id (material_request_id),
            INDEX idx_supplier (supplier_name),
            INDEX idx_status (status)
        )
        """)
        
        # Offer Items table
        cursor.execute("""
        CREATE TABLE offer_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            offer_id INT NOT NULL,
            material_request_item_id INT,
            description TEXT NOT NULL,
            specification TEXT,
            quantity DECIMAL(10,2) NOT NULL,
            unit VARCHAR(20) NOT NULL,
            unit_price DECIMAL(15,2) NOT NULL,
            total_price DECIMAL(15,2) NOT NULL,
            manufacturer VARCHAR(100),
            model_number VARCHAR(100),
            delivery_time VARCHAR(100),
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
            FOREIGN KEY (material_request_item_id) REFERENCES material_request_items(id),
            INDEX idx_offer_id (offer_id)
        )
        """)
        
        # Evaluations table
        cursor.execute("""
        CREATE TABLE evaluations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            material_request_id INT NOT NULL,
            offer_id INT NOT NULL,
            evaluator_id INT NOT NULL,
            evaluation_type VARCHAR(20) NOT NULL,
            criteria_scores JSON,
            total_score DECIMAL(5,2),
            comments TEXT,
            recommendation VARCHAR(20),
            status VARCHAR(20) NOT NULL DEFAULT 'DRAFT',
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            submitted_at DATETIME,
            FOREIGN KEY (material_request_id) REFERENCES material_requests(id) ON DELETE CASCADE,
            FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
            FOREIGN KEY (evaluator_id) REFERENCES users(id),
            INDEX idx_mr_id (material_request_id),
            INDEX idx_offer_id (offer_id),
            INDEX idx_evaluator_id (evaluator_id),
            INDEX idx_type (evaluation_type)
        )
        """)
        
        # Attachments table
        cursor.execute("""
        CREATE TABLE attachments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            material_request_id INT NOT NULL,
            uploaded_by_id INT NOT NULL,
            filename VARCHAR(255) NOT NULL,
            original_filename VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size INT NOT NULL,
            mime_type VARCHAR(100),
            description TEXT,
            category VARCHAR(50),
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (material_request_id) REFERENCES material_requests(id) ON DELETE CASCADE,
            FOREIGN KEY (uploaded_by_id) REFERENCES users(id),
            INDEX idx_mr_id (material_request_id),
            INDEX idx_uploaded_by (uploaded_by_id),
            INDEX idx_category (category)
        )
        """)
        
        # Templates table
        cursor.execute("""
        CREATE TABLE templates (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            category VARCHAR(50),
            template_data JSON NOT NULL,
            created_by_id INT NOT NULL,
            is_public BOOLEAN NOT NULL DEFAULT FALSE,
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            usage_count INT NOT NULL DEFAULT 0,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by_id) REFERENCES users(id),
            INDEX idx_created_by (created_by_id),
            INDEX idx_category (category),
            INDEX idx_public (is_public)
        )
        """)
        
        # User Activities table
        cursor.execute("""
        CREATE TABLE user_activities (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            action VARCHAR(100) NOT NULL,
            description TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            entity_type VARCHAR(50),
            entity_id INT,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_id (user_id),
            INDEX idx_action (action),
            INDEX idx_entity (entity_type, entity_id),
            INDEX idx_created_at (created_at)
        )
        """)
        
        connection.commit()
        print("✅ All database tables created successfully")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Database creation failed: {e}")
        return False

def insert_initial_data():
    """Insert initial data for the system"""
    print("\n📊 Inserting initial data...")
    
    try:
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            database='mets_system',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # Create admin user
        from werkzeug.security import generate_password_hash
        password_hash = generate_password_hash('admin123')
        
        cursor.execute("""
        INSERT INTO users (username, email, password_hash, first_name, last_name, role, department, is_admin, employee_id)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, ('admin', '<EMAIL>', password_hash, 'System', 'Administrator', 'ADMIN', 'IT', True, 'EMP001'))
        
        # Create sample users
        sample_users = [
            ('john.smith', '<EMAIL>', 'John', 'Smith', 'MATERIAL_SPECIALIST', 'Maintenance', 'EMP002'),
            ('sarah.jones', '<EMAIL>', 'Sarah', 'Jones', 'TECHNICAL_BUYER', 'Procurement', 'EMP003'),
            ('mike.wilson', '<EMAIL>', 'Mike', 'Wilson', 'SECTION_HEAD_EXPAT', 'Maintenance', 'EMP004'),
            ('lisa.brown', '<EMAIL>', 'Lisa', 'Brown', 'MATERIAL_COORDINATOR', 'Procurement', 'EMP005'),
            ('david.garcia', '<EMAIL>', 'David', 'Garcia', 'MAINTENANCE_MANAGER', 'Maintenance', 'EMP006'),
            ('anna.lee', '<EMAIL>', 'Anna', 'Lee', 'COMMERCIAL_COMMITTEE', 'Finance', 'EMP007'),
            ('robert.taylor', '<EMAIL>', 'Robert', 'Taylor', 'WAREHOUSE_KEEPER', 'Warehouse', 'EMP008'),
        ]
        
        for username, email, first_name, last_name, role, department, emp_id in sample_users:
            password_hash = generate_password_hash('password123')
            cursor.execute("""
            INSERT INTO users (username, email, password_hash, first_name, last_name, role, department, employee_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (username, email, password_hash, first_name, last_name, role, department, emp_id))
        
        # Create groups
        groups = [
            ('Material Specialists', 'Material request creators and specialists'),
            ('Technical Buyers', 'Technical evaluation and purchasing team'),
            ('Section Heads', 'Department section heads'),
            ('Maintenance Managers', 'Maintenance department managers'),
            ('Commercial Committee', 'Commercial evaluation committee'),
            ('Warehouse Team', 'Warehouse and inventory management'),
            ('Supervisors', 'Department supervisors')
        ]
        
        for name, description in groups:
            cursor.execute("""
            INSERT INTO groups (name, description) VALUES (%s, %s)
            """, (name, description))
        
        # Create default workflow template
        cursor.execute("""
        INSERT INTO workflow_templates (name, description, is_default) 
        VALUES (%s, %s, %s)
        """, ('Standard Material Request Workflow', 'Standard 8-stage material request approval workflow', True))
        
        # Insert workflow steps
        workflow_steps = [
            (1, 1, 'MR_CREATION', 'MR Creation', 'Material Request creation by specialists', 'MATERIAL_SPECIALIST', 24),
            (1, 2, 'MR_APPROVAL', 'MR Approval', 'Multi-level approval process', 'SECTION_HEAD_EXPAT', 48),
            (1, 3, 'RECEIVING_OFFERS', 'Receiving Offers', 'Collecting vendor offers and quotations', 'MATERIAL_COORDINATOR', 120),
            (1, 4, 'TECHNICAL_EVAL', 'Technical Evaluation', 'Technical evaluation of offers', 'TECHNICAL_BUYER', 72),
            (1, 5, 'COMMERCIAL_EVAL', 'Commercial Evaluation', 'Commercial evaluation and comparison', 'COMMERCIAL_COMMITTEE', 48),
            (1, 6, 'MANAGEMENT_APPR', 'Management Approval', 'Final management approval', 'MAINTENANCE_MANAGER', 24),
            (1, 7, 'PURCHASING_PROCESS', 'Purchasing Process', 'Purchase order creation and processing', 'MATERIAL_COORDINATOR', 48),
            (1, 8, 'CLOSING_OUT', 'Closing Out', 'Delivery tracking and completion', 'WAREHOUSE_KEEPER', 24)
        ]
        
        for template_id, step_num, stage_name, display_name, description, required_role, sla_hours in workflow_steps:
            cursor.execute("""
            INSERT INTO workflow_steps (template_id, step_number, stage_name, display_name, description, required_role, sla_hours)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (template_id, step_num, stage_name, display_name, description, required_role, sla_hours))
        
        connection.commit()
        print("✅ Initial data inserted successfully")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Initial data insertion failed: {e}")
        return False

def main():
    """Main setup function"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    METS Complete Database                    ║
    ║              Creating Full System Schema                    ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    steps_completed = 0
    total_steps = 2
    
    # Step 1: Create complete database
    if create_complete_database():
        steps_completed += 1
        print("✅ Database schema created")
    else:
        print("❌ Database schema creation failed")
        return False
    
    # Step 2: Insert initial data
    if insert_initial_data():
        steps_completed += 1
        print("✅ Initial data inserted")
    else:
        print("❌ Initial data insertion failed")
        return False
    
    print(f"\n📊 Setup Results: {steps_completed}/{total_steps} steps completed")
    
    if steps_completed == total_steps:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🎉 DATABASE COMPLETE! 🎉                 ║
    ║                                                              ║
    ║  Complete METS database schema has been created.            ║
    ║  All tables, relationships, and initial data ready.         ║
    ║                                                              ║
    ║  Ready for full application development!                    ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return True
    else:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    ⚠️  SETUP INCOMPLETE ⚠️                  ║
    ║                                                              ║
    ║  Database setup encountered issues.                         ║
    ║  Check the error messages above.                             ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
