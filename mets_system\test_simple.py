#!/usr/bin/env python3
"""
Simple test script for METS system
Tests if we can run the basic application
"""

import sys
import os
from datetime import datetime

def test_app_startup():
    """Test if the app can start"""
    print("🧪 Testing METS application startup...")
    
    try:
        # Set environment variables
        os.environ['FLASK_ENV'] = 'testing'
        os.environ['MYSQL_HOST'] = 'localhost'
        os.environ['MYSQL_USER'] = 'root'
        os.environ['MYSQL_PASSWORD'] = ''
        os.environ['MYSQL_DATABASE'] = 'mets_system_test'
        
        # Import the main app
        from app import create_app
        
        # Create app in testing mode
        app = create_app('testing')
        
        print("✅ METS application created successfully")
        print(f"   - App name: {app.name}")
        print(f"   - Testing mode: {app.testing}")
        
        # Test if we can create a test client
        with app.test_client() as client:
            print("✅ Test client created successfully")
            
            # Test if we can access a basic route (this will fail but shows routing works)
            try:
                response = client.get('/')
                print(f"✅ Basic route accessible (status: {response.status_code})")
            except Exception as e:
                print(f"⚠️  Route test failed (expected): {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ App startup failed: {e}")
        return False

def test_database_setup():
    """Test database setup"""
    print("\n🧪 Testing database setup...")
    
    try:
        import pymysql
        
        # Connect to MySQL
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # Create test database
        cursor.execute("CREATE DATABASE IF NOT EXISTS mets_system_test")
        print("✅ Test database created")
        
        # Check if we can use it
        cursor.execute("USE mets_system_test")
        print("✅ Test database accessible")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False

def main():
    """Run simple tests"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    METS System - Simple Test                 ║
    ║              Testing Application Startup                     ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    print(f"Python version: {sys.version}")
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Database setup
    if test_database_setup():
        tests_passed += 1
    
    # Test 2: App startup
    if test_app_startup():
        tests_passed += 1
    
    # Results
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🎉 ALL TESTS PASSED! 🎉                  ║
    ║                                                              ║
    ║  The METS system can start successfully!                    ║
    ║  Ready for full testing with database setup.                ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return True
    else:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    ⚠️  PARTIAL SUCCESS ⚠️                   ║
    ║                                                              ║
    ║  Some components need attention.                             ║
    ║  Check the error messages above.                             ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
