#!/usr/bin/env python3
"""
Quick fix for template errors in METS test app
"""

def fix_template_issues():
    """Fix common template issues"""
    print("🔧 Fixing template issues...")
    
    # Read the test_app.py file
    with open('test_app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix any remaining moment() references
    if 'moment()' in content:
        content = content.replace('moment().format(\'YYYY-MM-DD HH:mm:ss\')', 'current_time')
        print("✅ Fixed moment() references")
    
    # Ensure current_time is passed to all templates
    fixes_made = []
    
    # Fix dashboard template call
    if 'render_template_string(DASHBOARD_TEMPLATE, user=current_user, stats=stats)' in content:
        content = content.replace(
            'render_template_string(DASHBOARD_TEMPLATE, user=current_user, stats=stats)',
            'render_template_string(DASHBOARD_TEMPLATE, user=current_user, stats=stats, current_time=datetime.now().strftime(\'%Y-%m-%d %H:%M:%S\'))'
        )
        fixes_made.append("Dashboard template")
    
    # Write back the fixed content
    with open('test_app.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    if fixes_made:
        print(f"✅ Fixed templates: {', '.join(fixes_made)}")
    else:
        print("✅ No template issues found")
    
    return True

def test_template_syntax():
    """Test if templates have valid syntax"""
    print("\n🧪 Testing template syntax...")
    
    try:
        from jinja2 import Template
        
        # Test templates from the file
        with open('test_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract template strings
        templates = {
            'LOGIN_TEMPLATE': 'LOGIN_TEMPLATE = """',
            'DASHBOARD_TEMPLATE': 'DASHBOARD_TEMPLATE = """',
            'PROFILE_TEMPLATE': 'PROFILE_TEMPLATE = """',
            'TEST_DB_TEMPLATE': 'TEST_DB_TEMPLATE = """'
        }
        
        for template_name, start_marker in templates.items():
            try:
                start_idx = content.find(start_marker)
                if start_idx != -1:
                    start_idx += len(start_marker)
                    end_idx = content.find('"""', start_idx)
                    if end_idx != -1:
                        template_content = content[start_idx:end_idx]
                        
                        # Test template compilation
                        Template(template_content)
                        print(f"✅ {template_name}: Valid syntax")
                    else:
                        print(f"⚠️  {template_name}: Could not find end marker")
                else:
                    print(f"⚠️  {template_name}: Could not find template")
            except Exception as e:
                print(f"❌ {template_name}: Syntax error - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Template testing failed: {e}")
        return False

def main():
    """Main fix function"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    METS Template Fixer                       ║
    ║              Fixing Jinja2 Template Issues                  ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    success = True
    
    # Fix template issues
    if fix_template_issues():
        print("✅ Template fixes applied")
    else:
        print("❌ Template fixes failed")
        success = False
    
    # Test template syntax
    if test_template_syntax():
        print("✅ Template syntax validation passed")
    else:
        print("❌ Template syntax validation failed")
        success = False
    
    if success:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🎉 FIXES APPLIED! 🎉                     ║
    ║                                                              ║
    ║  Template issues have been resolved.                        ║
    ║  The application should now work without errors.            ║
    ║                                                              ║
    ║  Restart the test app: python test_app.py                   ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
    else:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    ⚠️  FIXES INCOMPLETE ⚠️                  ║
    ║                                                              ║
    ║  Some template issues could not be resolved automatically.  ║
    ║  Please check the error messages above.                     ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
    
    return success

if __name__ == '__main__':
    main()
