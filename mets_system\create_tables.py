#!/usr/bin/env python3
"""
Create database tables for METS system using SQLAlchemy
"""

import os
import sys
from datetime import datetime

def create_tables():
    """Create database tables using SQLAlchemy"""
    print("🗄️  Creating database tables...")
    
    try:
        # Set environment variables
        os.environ['FLASK_ENV'] = 'development'
        os.environ['MYSQL_HOST'] = 'localhost'
        os.environ['MYSQL_USER'] = 'root'
        os.environ['MYSQL_PASSWORD'] = ''
        os.environ['MYSQL_DATABASE'] = 'mets_system'
        
        # Import the app and database
        from app import create_app, db
        
        # Create app
        app = create_app('development')
        
        with app.app_context():
            # Create all tables
            db.create_all()
            print("✅ Database tables created successfully")
            
            # Check what tables were created
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            print(f"✅ Created {len(tables)} tables: {', '.join(tables)}")
            
            return True
            
    except Exception as e:
        print(f"❌ Table creation failed: {e}")
        return False

def create_admin_user():
    """Create default admin user"""
    print("\n👤 Creating admin user...")
    
    try:
        # Import models
        from app import create_app, db
        from app.models.user import User
        
        # Create app
        app = create_app('development')
        
        with app.app_context():
            # Check if admin exists
            admin = User.query.filter_by(username='admin').first()
            
            if admin:
                print("✅ Admin user already exists")
                return True
            
            # Create admin user
            admin = User(
                username='admin',
                email='<EMAIL>',
                first_name='System',
                last_name='Administrator',
                role='ADMIN',
                department='IT',
                is_admin=True,
                employee_id='EMP001'
            )
            admin.set_password('admin123')
            
            db.session.add(admin)
            db.session.commit()
            
            print("✅ Admin user created successfully")
            print("   Username: admin")
            print("   Password: admin123")
            
            return True
            
    except Exception as e:
        print(f"❌ Admin user creation failed: {e}")
        return False

def create_sample_users():
    """Create sample users"""
    print("\n👥 Creating sample users...")
    
    try:
        from app import create_app, db
        from app.models.user import User
        
        app = create_app('development')
        
        with app.app_context():
            sample_users = [
                {
                    'username': 'john.smith',
                    'email': '<EMAIL>',
                    'first_name': 'John',
                    'last_name': 'Smith',
                    'role': 'MATERIAL_SPECIALIST',
                    'department': 'Maintenance',
                    'employee_id': 'EMP002'
                },
                {
                    'username': 'sarah.jones',
                    'email': '<EMAIL>',
                    'first_name': 'Sarah',
                    'last_name': 'Jones',
                    'role': 'TECHNICAL_BUYER',
                    'department': 'Procurement',
                    'employee_id': 'EMP003'
                },
                {
                    'username': 'mike.wilson',
                    'email': '<EMAIL>',
                    'first_name': 'Mike',
                    'last_name': 'Wilson',
                    'role': 'SECTION_HEAD_EXPAT',
                    'department': 'Maintenance',
                    'employee_id': 'EMP004'
                }
            ]
            
            created = 0
            for user_data in sample_users:
                existing = User.query.filter_by(username=user_data['username']).first()
                if not existing:
                    user = User(**user_data)
                    user.set_password('password123')
                    db.session.add(user)
                    created += 1
            
            db.session.commit()
            print(f"✅ Created {created} sample users")
            
            return True
            
    except Exception as e:
        print(f"❌ Sample user creation failed: {e}")
        return False

def verify_setup():
    """Verify the database setup"""
    print("\n🔍 Verifying database setup...")
    
    try:
        from app import create_app, db
        from app.models.user import User
        
        app = create_app('development')
        
        with app.app_context():
            # Check tables
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            print(f"✅ Found {len(tables)} tables")
            
            # Check users
            user_count = User.query.count()
            print(f"✅ Found {user_count} users")
            
            # Check admin
            admin = User.query.filter_by(username='admin').first()
            if admin:
                print("✅ Admin user verified")
            else:
                print("⚠️  Admin user not found")
            
            return len(tables) > 0 and user_count > 0
            
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main setup function"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    METS Database Setup                       ║
    ║              Creating Tables and Users                      ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    steps_completed = 0
    total_steps = 4
    
    # Step 1: Create tables
    if create_tables():
        steps_completed += 1
    else:
        print("❌ Cannot proceed without tables")
        return False
    
    # Step 2: Create admin user
    if create_admin_user():
        steps_completed += 1
    
    # Step 3: Create sample users
    if create_sample_users():
        steps_completed += 1
    
    # Step 4: Verify setup
    if verify_setup():
        steps_completed += 1
    
    print(f"\n📊 Setup Results: {steps_completed}/{total_steps} steps completed")
    
    if steps_completed >= 3:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🎉 DATABASE READY! 🎉                    ║
    ║                                                              ║
    ║  The METS database has been set up successfully.            ║
    ║  You can now run the application with: python app.py        ║
    ║                                                              ║
    ║  Default login credentials:                                  ║
    ║  Username: admin                                             ║
    ║  Password: admin123                                          ║
    ║                                                              ║
    ║  Sample user credentials:                                    ║
    ║  Username: john.smith / sarah.jones / mike.wilson           ║
    ║  Password: password123                                       ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return True
    else:
        print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    ⚠️  SETUP INCOMPLETE ⚠️                  ║
    ║                                                              ║
    ║  Database setup encountered issues.                         ║
    ║  Check the error messages above.                             ║
    ╚══════════════════════════════════════════════════════════════╝
        """)
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
