from flask import Blueprint, request, jsonify, send_file, current_app
from flask_login import login_required, current_user
from datetime import datetime
import os
from app import db
from app.models.user import User
from app.models.material_request import MaterialRequest, MaterialRequestItem
from app.models.workflow import Offer, Evaluation
from app.models.attachment import Attachment, Template

api_bp = Blueprint('api', __name__)

@api_bp.route('/mr/<int:mr_id>')
@login_required
def get_mr(mr_id):
    """Get Material Request data as JSON"""
    mr = MaterialRequest.query.get_or_404(mr_id)
    
    # Check permissions
    if not can_view_mr(mr, current_user):
        return jsonify({'error': 'Access denied'}), 403
    
    # Get related data
    items = [item.to_dict() for item in mr.items]
    attachments = [att.to_dict() for att in mr.attachments if att.is_active]
    workflow_history = [action.to_dict() for action in mr.workflow_actions.order_by('created_at')]
    
    mr_data = mr.to_dict()
    mr_data['items'] = items
    mr_data['attachments'] = attachments
    mr_data['workflow_history'] = workflow_history
    
    return jsonify(mr_data)

@api_bp.route('/mr/<int:mr_id>/items', methods=['GET', 'POST'])
@login_required
def mr_items(mr_id):
    """Get or add items to Material Request"""
    mr = MaterialRequest.query.get_or_404(mr_id)
    
    if not mr.can_edit(current_user):
        return jsonify({'error': 'Access denied'}), 403
    
    if request.method == 'GET':
        items = [item.to_dict() for item in mr.items]
        return jsonify(items)
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            
            item = MaterialRequestItem(
                material_request_id=mr.id,
                description=data.get('description', ''),
                specification=data.get('specification', ''),
                quantity=float(data.get('quantity', 0)),
                unit=data.get('unit', ''),
                unit_price=float(data.get('unit_price', 0)) if data.get('unit_price') else None,
                manufacturer=data.get('manufacturer', ''),
                model_number=data.get('model_number', ''),
                remarks=data.get('remarks', '')
            )
            
            # Calculate total price
            if item.unit_price and item.quantity:
                item.total_price = item.unit_price * item.quantity
            
            db.session.add(item)
            db.session.commit()
            
            return jsonify(item.to_dict()), 201
            
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 400

@api_bp.route('/mr/items/<int:item_id>', methods=['PUT', 'DELETE'])
@login_required
def mr_item(item_id):
    """Update or delete Material Request item"""
    item = MaterialRequestItem.query.get_or_404(item_id)
    mr = item.material_request
    
    if not mr.can_edit(current_user):
        return jsonify({'error': 'Access denied'}), 403
    
    if request.method == 'PUT':
        try:
            data = request.get_json()
            
            item.description = data.get('description', item.description)
            item.specification = data.get('specification', item.specification)
            item.quantity = float(data.get('quantity', item.quantity))
            item.unit = data.get('unit', item.unit)
            item.unit_price = float(data.get('unit_price')) if data.get('unit_price') else item.unit_price
            item.manufacturer = data.get('manufacturer', item.manufacturer)
            item.model_number = data.get('model_number', item.model_number)
            item.remarks = data.get('remarks', item.remarks)
            
            # Recalculate total price
            if item.unit_price and item.quantity:
                item.total_price = item.unit_price * item.quantity
            
            item.updated_at = datetime.utcnow()
            db.session.commit()
            
            return jsonify(item.to_dict())
            
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 400
    
    elif request.method == 'DELETE':
        try:
            db.session.delete(item)
            db.session.commit()
            return jsonify({'message': 'Item deleted successfully'})
            
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 400

@api_bp.route('/attachments/<int:attachment_id>/download')
@login_required
def download_attachment(attachment_id):
    """Download attachment file"""
    attachment = Attachment.query.get_or_404(attachment_id)
    mr = attachment.material_request
    
    # Check permissions
    if not can_view_mr(mr, current_user):
        return jsonify({'error': 'Access denied'}), 403
    
    try:
        return send_file(
            attachment.file_path,
            as_attachment=True,
            download_name=attachment.original_filename,
            mimetype=attachment.mime_type
        )
    except FileNotFoundError:
        return jsonify({'error': 'File not found'}), 404

@api_bp.route('/attachments/<int:attachment_id>/preview')
@login_required
def preview_attachment(attachment_id):
    """Preview attachment file (for images)"""
    attachment = Attachment.query.get_or_404(attachment_id)
    mr = attachment.material_request
    
    # Check permissions
    if not can_view_mr(mr, current_user):
        return jsonify({'error': 'Access denied'}), 403
    
    if not attachment.is_image:
        return jsonify({'error': 'File is not an image'}), 400
    
    try:
        return send_file(
            attachment.file_path,
            mimetype=attachment.mime_type
        )
    except FileNotFoundError:
        return jsonify({'error': 'File not found'}), 404

@api_bp.route('/attachments/<int:attachment_id>', methods=['DELETE'])
@login_required
def delete_attachment(attachment_id):
    """Delete attachment"""
    attachment = Attachment.query.get_or_404(attachment_id)
    mr = attachment.material_request
    
    # Check permissions
    if not mr.can_edit(current_user) and attachment.uploaded_by_id != current_user.id:
        return jsonify({'error': 'Access denied'}), 403
    
    try:
        # Delete physical file
        attachment.delete_file()
        
        # Delete database record
        db.session.delete(attachment)
        db.session.commit()
        
        return jsonify({'message': 'Attachment deleted successfully'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 400

@api_bp.route('/users/search')
@login_required
def search_users():
    """Search users for assignment"""
    query = request.args.get('q', '').strip()
    role = request.args.get('role', '')
    
    if len(query) < 2:
        return jsonify([])
    
    # Build search query
    search_query = User.query.filter(User.is_active == True)
    
    if role:
        search_query = search_query.filter(User.role == role)
    
    search_query = search_query.filter(
        db.or_(
            User.username.contains(query),
            User.first_name.contains(query),
            User.last_name.contains(query),
            User.email.contains(query)
        )
    ).limit(10)
    
    users = search_query.all()
    
    return jsonify([{
        'id': user.id,
        'username': user.username,
        'full_name': user.full_name,
        'email': user.email,
        'role': user.role,
        'department': user.department
    } for user in users])

@api_bp.route('/dashboard/stats')
@login_required
def dashboard_stats():
    """Get dashboard statistics"""
    from app.routes.main import get_dashboard_stats, get_workflow_stats
    
    stats = get_dashboard_stats()
    workflow_stats = get_workflow_stats()
    
    return jsonify({
        'general_stats': stats,
        'workflow_stats': workflow_stats
    })

@api_bp.route('/mr/<int:mr_id>/offers')
@login_required
def get_offers(mr_id):
    """Get offers for Material Request"""
    mr = MaterialRequest.query.get_or_404(mr_id)
    
    if not can_view_mr(mr, current_user):
        return jsonify({'error': 'Access denied'}), 403
    
    offers = [offer.to_dict() for offer in mr.offers]
    return jsonify(offers)

@api_bp.route('/offers/<int:offer_id>/evaluate', methods=['POST'])
@login_required
def evaluate_offer(offer_id):
    """Submit evaluation for offer"""
    offer = Offer.query.get_or_404(offer_id)
    mr = offer.material_request
    
    # Check permissions
    if not can_evaluate(mr, current_user):
        return jsonify({'error': 'Access denied'}), 403
    
    try:
        data = request.get_json()
        
        # Determine evaluation type
        evaluation_type = 'TECHNICAL' if mr.current_stage == 'TECHNICAL_EVAL' else 'COMMERCIAL'
        
        # Check if evaluation already exists
        evaluation = Evaluation.query.filter_by(
            offer_id=offer.id,
            evaluator_id=current_user.id,
            evaluation_type=evaluation_type
        ).first()
        
        if not evaluation:
            evaluation = Evaluation(
                material_request_id=mr.id,
                offer_id=offer.id,
                evaluator_id=current_user.id,
                evaluation_type=evaluation_type
            )
            db.session.add(evaluation)
        
        # Update evaluation
        evaluation.total_score = float(data.get('total_score', 0))
        evaluation.comments = data.get('comments', '')
        evaluation.recommendation = data.get('recommendation', 'ACCEPT')
        evaluation.criteria_scores = data.get('criteria_scores', {})
        evaluation.status = 'SUBMITTED'
        evaluation.submitted_at = datetime.utcnow()
        
        # Update offer scores
        if evaluation_type == 'TECHNICAL':
            offer.technical_score = evaluation.total_score
        else:
            offer.commercial_score = evaluation.total_score
        
        # Calculate overall score
        if offer.technical_score and offer.commercial_score:
            offer.overall_score = (offer.technical_score + offer.commercial_score) / 2
        
        db.session.commit()
        
        return jsonify(evaluation.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 400

@api_bp.route('/templates')
@login_required
def get_templates():
    """Get available templates"""
    templates = Template.query.filter(
        db.or_(
            Template.is_public == True,
            Template.created_by_id == current_user.id
        )
    ).filter_by(is_active=True).all()
    
    return jsonify([template.to_dict() for template in templates])

@api_bp.route('/templates', methods=['POST'])
@login_required
def create_template():
    """Create new template"""
    try:
        data = request.get_json()
        
        template = Template(
            name=data.get('name', ''),
            description=data.get('description', ''),
            category=data.get('category', ''),
            template_data=data.get('template_data', {}),
            created_by_id=current_user.id,
            is_public=bool(data.get('is_public', False))
        )
        
        db.session.add(template)
        db.session.commit()
        
        return jsonify(template.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 400

@api_bp.route('/export/mr/<int:mr_id>')
@login_required
def export_mr(mr_id):
    """Export Material Request as PDF"""
    mr = MaterialRequest.query.get_or_404(mr_id)
    
    if not can_view_mr(mr, current_user):
        return jsonify({'error': 'Access denied'}), 403
    
    try:
        from app.services.pdf_service import generate_mr_pdf
        
        pdf_path = generate_mr_pdf(mr)
        
        return send_file(
            pdf_path,
            as_attachment=True,
            download_name=f'MR_{mr.mr_number}.pdf',
            mimetype='application/pdf'
        )
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/export/register')
@login_required
def export_register():
    """Export Material Register as Excel"""
    try:
        from app.services.excel_service import generate_register_excel
        
        # Get filter parameters
        filters = {
            'status': request.args.get('status'),
            'stage': request.args.get('stage'),
            'date_from': request.args.get('date_from'),
            'date_to': request.args.get('date_to'),
            'creator': request.args.get('creator')
        }
        
        excel_path = generate_register_excel(filters)
        
        return send_file(
            excel_path,
            as_attachment=True,
            download_name=f'Material_Register_{datetime.now().strftime("%Y%m%d")}.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def can_view_mr(mr, user):
    """Check if user can view the MR"""
    if user.is_admin:
        return True
    
    # Creator can always view
    if mr.created_by_id == user.id:
        return True
    
    # Assigned user can view
    if mr.assigned_to_id == user.id:
        return True
    
    # Users who have acted on the MR can view
    if mr.workflow_actions.filter_by(user_id=user.id).first():
        return True
    
    return False

def can_evaluate(mr, user):
    """Check if user can evaluate offers for MR"""
    if user.is_admin:
        return True
    
    # Check if user can evaluate based on current stage and role
    if mr.current_stage == 'TECHNICAL_EVAL':
        return user.role in ['TECHNICAL_BUYER', 'SUPERVISOR', 'SECTION_HEAD_EXPAT', 'SECTION_HEAD_LOCAL']
    elif mr.current_stage == 'COMMERCIAL_EVAL':
        return user.role in ['COMMERCIAL_COMMITTEE', 'SECTION_HEAD_EXPAT', 'SECTION_HEAD_LOCAL']
    
    return False
